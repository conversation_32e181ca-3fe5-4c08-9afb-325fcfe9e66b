<?php
/**
 * Correzioni apportate (2025-05-19):
 * - Aggiunto hook wp_ajax_nopriv_save_document_analysis per consentire agli utenti non autenticati di salvare le analisi
 * - Aggiunto hook wp_ajax_nopriv_export_analysis_pdf per consentire agli utenti non autenticati di esportare in PDF
 * - Implementata la chiamata a do_action('save_document_analysis', ...) per aggiornare le statistiche utente
 * - Aggiunto calcolo e tracciamento dei token utilizzati per il corretto aggiornamento delle statistiche
 */

// Inizializza la sessione prima di qualsiasi output
if (!session_id() && !headers_sent()) {
    session_start();
}

/**
 * Plugin Name: Financial Advisor Plugin
 * Plugin URI: https://example.com/document-viewer-plugin
 * Description: AI to analyze document and make analysis. Supports widget and shortcode.
 * Version: 1.1
 * Author: Inventyx
 * Author URI: https://inventyx.tech
 */

if (!defined('ABSPATH')) {
    exit;
}

// Define plugin version constant
if (!defined('DOCUMENT_ADVISOR_VERSION')) {
    define('DOCUMENT_ADVISOR_VERSION', '1.1');
}

// Load payment gateway testing functionality
require_once(plugin_dir_path(__FILE__) . 'includes/payment-gateway-testing.php');

// Includi l'autoloader di Composer
require_once __DIR__ . '/vendor/autoload.php';

// Add debugging constant
if (!defined('DOCUMENT_VIEWER_DEBUG')) {
    define('DOCUMENT_VIEWER_DEBUG', true); // set to true to enable debug logging
}

/**
 * Filtro di fallback per il redirect di login
 *
 * Questo filtro fornisce un modo standardizzato per gestire i redirect dopo il login.
 * Viene utilizzato dai componenti che necessitano di reindirizzare gli utenti dopo l'autenticazione.
 */
add_filter('fa_login_redirect', function($redirect_to) {
    $global_redirect_url = get_option('login_global_redirect_url', '');
    return !empty($global_redirect_url) ? $global_redirect_url : home_url();
});

// Include widget files
require_once plugin_dir_path(__FILE__) . 'includes/widgets/chat-model-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/document-viewer-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/document-analizer/document-analizer-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/document-analizer/document-analizer-shortcode.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/user-subscription-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/login-widget.php';
require_once plugin_dir_path(__FILE__) . 'includes/widgets/subscriber-management-widget.php';

// Include logger class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-viewer-logger.php';

// Include access control class
require_once plugin_dir_path(__FILE__) . 'includes/class-fa-access-control.php';

// Include database setup file
require_once plugin_dir_path(__FILE__) . 'includes/database-setup.php';

// Include access helper functions
require_once plugin_dir_path(__FILE__) . 'includes/access-helper.php';

// Include template functions
require_once plugin_dir_path(__FILE__) . 'includes/template-functions.php';

// Include shortcodes
require_once plugin_dir_path(__FILE__) . 'includes/shortcodes.php';

// Include template loader
require_once plugin_dir_path(__FILE__) . 'includes/template-loader.php';

// Include document management class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-management.php';

// Include document stats class
require_once plugin_dir_path(__FILE__) . 'includes/class-document-stats.php';

// Include Financial Academy Manager class
require_once plugin_dir_path(__FILE__) . 'includes/class-financial-academy-manager.php';

// Include Menu Manager class - NUOVO!
require_once plugin_dir_path(__FILE__) . 'includes/class-menu-manager.php';

// Include Widget Help Migration class FIRST (needed by system)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-migration.php';

// Include Widget Help Manager class SECOND (new system)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-manager.php';

// Include Widget Help System class THIRD (legacy compatibility)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-system.php';

// Include Widget Help Admin class
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-admin.php';

// Include Widget Help Admin Simple class (New Simplified Interface)
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-admin-simple.php';

// Include Widget Help Simple Migration class
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-simple-migration.php';

// Include Widget Help Simple Migration
require_once plugin_dir_path(__FILE__) . 'includes/class-widget-help-simple-migration.php';

// Include User Subscription Manager class
require_once plugin_dir_path(__FILE__) . 'includes/class-user-subscription-manager.php';

// Include User Subscription Admin class
require_once plugin_dir_path(__FILE__) . 'includes/class-user-subscription-admin.php';

// Include Payment Gateway Admin class
// Include Enhanced Payment Gateway Test first
require_once plugin_dir_path(__FILE__) . 'admin-payment-gateway-test.php';

// Include Payment Gateway Admin after test file
require_once plugin_dir_path(__FILE__) . 'includes/class-payment-gateway-admin.php';

// Include Office Add-in integration
require_once plugin_dir_path(__FILE__) . 'office-addin.php';

// Include Office Add-in manifest generator
require_once plugin_dir_path(__FILE__) . 'includes/class-office-addin-manifest.php';

// Backward compatibility wrapper functions
function dv_debug_log($msg, $context = 'default') {
    dv_logger()->debug_log($msg, $context);
}

function dv_clear_shared_log($context = 'all') {
    return dv_logger()->clear_shared_log();
}

function dv_get_shared_log($context = 'all') {
    return dv_logger()->get_log_content($context);
}

class Document_Viewer_Plugin {
    private $version = '1.1';
    private $plugin_url = '';
    private $author = 'Your Name';
    private $author_profile = '';
    private $logger;

    public function __construct() {
        // Initialize the logger
        $this->logger = dv_logger();

        // Initialize properties
        $this->plugin_url = plugin_dir_url(__FILE__);
        $this->author_profile = 'https://example.com';

        // Require settings file early
        require_once plugin_dir_path(__FILE__) . 'settings.php';

        // Add shortcodes
        add_shortcode('document_viewer', array($this, 'render_document_viewer'));
        add_shortcode('chat_viewer', array($this, 'render_chat_viewer'));
        add_shortcode('document_analizer', 'render_document_analizer_shortcode');

        // Core actions
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('widgets_init', array($this, 'register_widget'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'create_document_analysis_table'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'create_preset_queries_table'));
        add_action('activate_financial-advisor-V4/document-advisor-plugin.php', array($this, 'initialize_database_tables'));

        // AJAX handlers for document operations
        add_action('wp_ajax_clear_document', array($this, 'clear_document'));
        // Handle document analysis for logged-in users
        add_action('wp_ajax_analyze_document', array($this, 'analyze_document'));
        // Also handle document analysis for non-logged-in users with proper authentication
        add_action('wp_ajax_nopriv_analyze_document', array($this, 'analyze_document'));
        add_action('wp_ajax_export_analysis_pdf', array($this, 'export_analysis_pdf'));
        add_action('wp_ajax_nopriv_export_analysis_pdf', array($this, 'export_analysis_pdf')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_chat_model', array($this, 'chat_model'));
        add_action('wp_ajax_nopriv_chat_model', array($this, 'chat_model')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_save_document_analysis', array($this, 'save_document_analysis'));
        add_action('wp_ajax_nopriv_save_document_analysis', array($this, 'save_document_analysis')); // Aggiungiamo l'hook per gli utenti non loggati
        add_action('wp_ajax_convert_word_to_pdf', array($this, 'convert_word_to_pdf'));
        add_action('wp_ajax_nopriv_convert_word_to_pdf', array($this, 'convert_word_to_pdf')); // Aggiungiamo l'hook per gli utenti non loggati

        // Debug hook per diagnosticare problemi con le sottoscrizioni utente
        add_action('wp_ajax_debug_user_subscription', array($this, 'debug_user_subscription'));
        add_action('wp_ajax_nopriv_debug_user_subscription', array($this, 'debug_user_subscription'));

        // AJAX handlers specifici per Document Analizer
        add_action('wp_ajax_analizer_clear_document', array($this, 'analizer_clear_document'));
        add_action('wp_ajax_analizer_analyze_document', array($this, 'analizer_analyze_document'));
        add_action('wp_ajax_nopriv_analizer_analyze_document', array($this, 'analizer_analyze_document'));
        add_action('wp_ajax_analizer_export_analysis_pdf', array($this, 'analizer_export_analysis_pdf'));
        add_action('wp_ajax_nopriv_analizer_export_analysis_pdf', array($this, 'analizer_export_analysis_pdf'));
        add_action('wp_ajax_analizer_save_analysis', array($this, 'analizer_save_analysis'));
        add_action('wp_ajax_nopriv_analizer_save_analysis', array($this, 'analizer_save_analysis'));
        add_action('wp_ajax_analizer_advanced_analysis', array($this, 'analizer_advanced_analysis'));
        add_action('wp_ajax_nopriv_analizer_advanced_analysis', array($this, 'analizer_advanced_analysis'));

        // Initialize admin classes
        if (is_admin()) {
            add_action('admin_init', array($this, 'init_admin_classes'));
        }
    }

    /**
     * Initialize admin classes
     */
    public function init_admin_classes() {
        // Initialize Payment Gateway Admin
        new Payment_Gateway_Admin();

        // Initialize Menu Manager
        Financial_Advisor_Menu_Manager::get_instance();
    }

    /**
     * Get current user information regardless of user type (WordPress or external)
     *
     * @return array User information with keys: id, type, data
     */
    public function get_current_user_info() {
        $user_info = [
            'id' => 0,
            'type' => 'guest',
            'data' => null
        ];

        // Check for WordPress user first
        $wp_user_id = get_current_user_id();
        if ($wp_user_id > 0) {
            $user_info['id'] = $wp_user_id;
            $user_info['type'] = 'wordpress';
            $user_info['data'] = get_userdata($wp_user_id);
            return $user_info;
        }

        // Check for external subscriber
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            if (fa_access_control()->is_current_user_subscriber()) {
                $subscriber_data = fa_access_control()->get_current_subscriber_data();
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $user_info['id'] = intval($subscriber_data['id']);
                    $user_info['type'] = 'subscriber';
                    $user_info['data'] = $subscriber_data;
                    return $user_info;
                }
            }
        }

        // Last resort: check cookie directly
        if (isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);
                if (isset($subscriber_data['id']) && intval($subscriber_data['id']) > 0) {
                    $user_info['id'] = intval($subscriber_data['id']);
                    $user_info['type'] = 'subscriber';
                    $user_info['data'] = $subscriber_data;
                    return $user_info;
                }
            } catch (Exception $e) {
                dv_debug_log('Error decoding subscriber cookie: ' . $e->getMessage());
            }
        }

        return $user_info;
    }

    /**
     * Update user statistics in a transactional manner
     *
     * @param int $user_id User ID
     * @param string $user_type User type (wordpress|subscriber)
     * @param array $stats_update Statistics to update
     * @return bool Success or failure
     */
    public function update_user_stats_transaction($user_id, $user_type, $stats_update) {
        global $wpdb;

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            $success = false;

            if ($user_type === 'subscriber') {
                $users_table = 'wpcd_user_subscription';

                // Get current values
                $current = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$users_table} WHERE id = %d FOR UPDATE",
                    $user_id
                ));

                if (!$current) {
                    throw new Exception("User not found: {$user_id}");
                }

                // Calculate new values
                $updates = [];
                $formats = [];

                if (isset($stats_update['tokens_used'])) {
                    $updates['tokens_used'] = $current->tokens_used + $stats_update['tokens_used'];
                    $formats[] = '%d';
                }

                if (isset($stats_update['analysis_count'])) {
                    $updates['analysis_count'] = $current->analysis_count + $stats_update['analysis_count'];
                    $formats[] = '%d';
                }

                if (isset($stats_update['cost'])) {
                    $updates['actual_cost'] = $current->actual_cost + $stats_update['cost'];
                    $updates['tot_cost'] = $current->tot_cost + $stats_update['cost'];
                    // Supporta sia credit che credits_available
                    if (property_exists($current, 'credits_available')) {
                        $updates['credits_available'] = max(0, $current->credits_available - $stats_update['cost']);
                    } else {
                        $updates['credit'] = max(0, $current->credit - $stats_update['cost']);
                    }
                    $formats[] = '%f';
                    $formats[] = '%f';
                    $formats[] = '%f';
                }

                $updates['updated_at'] = current_time('mysql');
                $formats[] = '%s';

                // Update the database
                $result = $wpdb->update(
                    $users_table,
                    $updates,
                    ['id' => $user_id],
                    $formats,
                    ['%d']
                );

                $success = ($result !== false);
            } else if ($user_type === 'wordpress') {
                // Handle WordPress user updates using Document_Stats class
                if (class_exists('Document_Stats')) {
                    $stats = new Document_Stats();

                    // Get current stats
                    $current_stats = $stats->get_user_stats($user_id);

                    if (!$current_stats) {
                        $stats->initialize_user_stats($user_id);
                        $current_stats = $stats->get_user_stats($user_id);
                    }

                    // Update WordPress user stats
                    $updates = [
                        'analysis_count' => $current_stats->analysis_count,
                        'tokens_used' => $current_stats->tokens_used,
                        'credits_available' => $current_stats->credits_available,
                        'actual_cost' => $current_stats->actual_cost,
                        'tot_cost' => $current_stats->tot_cost,
                        'last_update' => current_time('mysql')
                    ];

                    if (isset($stats_update['tokens_used'])) {
                        $updates['tokens_used'] += $stats_update['tokens_used'];
                    }

                    if (isset($stats_update['analysis_count'])) {
                        $updates['analysis_count'] += $stats_update['analysis_count'];
                    }

                    if (isset($stats_update['cost'])) {
                        $updates['actual_cost'] += $stats_update['cost'];
                        $updates['tot_cost'] += $stats_update['cost'];
                        $updates['credits_available'] = max(0, $updates['credits_available'] - $stats_update['cost']);
                    }

                    // Update the database
                    $result = $wpdb->update(
                        $stats->stats_table,
                        $updates,
                        ['user_id' => $user_id],
                        ['%d', '%d', '%f', '%f', '%f', '%s'],
                        ['%d']
                    );

                    $success = ($result !== false);
                } else {
                    throw new Exception("Document_Stats class not available");
                }
            }

            if (!$success) {
                throw new Exception("Failed to update user stats: {$wpdb->last_error}");
            }

            // Commit transaction
            $wpdb->query('COMMIT');
            return true;
        } catch (Exception $e) {
            // Rollback on error
            $wpdb->query('ROLLBACK');
            dv_debug_log('Transaction error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Debug delle sottoscrizioni utente
     * Funzione per diagnosticare problemi con i dati delle sottoscrizioni utente
     */
    public function debug_user_subscription() {
        global $wpdb;

        // Get user ID from cookie
        $external_user_id = 0;
        if (isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);
                if (isset($subscriber_data['id'])) {
                    $external_user_id = intval($subscriber_data['id']);
                }
            } catch (Exception $e) {
                wp_send_json_error(['error' => $e->getMessage()]);
            }
        }

        if ($external_user_id > 0) {
            $users_table = 'wpcd_user_subscription';
            $user = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$users_table} WHERE id = %d",
                $external_user_id
            ), ARRAY_A);

            wp_send_json_success([
                'user_data' => $user,
                'cookie_data' => $subscriber_data,
                'message' => 'Dati utente recuperati con successo'
            ]);
        } else {
            wp_send_json_error(['message' => 'Nessun ID utente valido trovato nel cookie']);
        }
    }

    /**
     * Enqueue scripts and styles for the plugin
     */    public function enqueue_scripts() {
        // Enqueue WordPress dashicons for the logout button
        wp_enqueue_style('dashicons');

        // Register and enqueue the main document viewer CSS
        wp_register_style(
            'document-viewer-style',
            $this->plugin_url . 'assets/css/document-viewer-main.css',
            array(),
            $this->version
        );
        wp_enqueue_style('document-viewer-style');

        // Document stats CSS viene caricato solo quando il widget document viewer è utilizzato
        // Rimosso da qui per evitare conflitti con altri widget



        // Register and enqueue the text enhancer CSS
        wp_register_style(
            'text-enhancer-style',
            $this->plugin_url . 'assets/css/text-enhancer.css',
            array('document-viewer-style'),
            $this->version
        );
        wp_enqueue_style('text-enhancer-style');

        // Register and enqueue the mega tooltip CSS
        wp_register_style(
            'mega-tooltip-style',
            $this->plugin_url . 'assets/css/mega-tooltip.css',
            array('document-viewer-style'),
            $this->version
        );
        wp_enqueue_style('mega-tooltip-style');

        // Register and enqueue the OCR script
        wp_register_script(
            'document-ocr-script',
            $this->plugin_url . 'assets/js/ocr.js',
            array('jquery'),
            $this->version,
            true
        );
          // Register and enqueue the document stats JavaScript
        wp_register_script(
            'document-stats-script',
            $this->plugin_url . 'assets/js/document-stats.js',
            array('jquery'),
            $this->version,
            true
        );

        // Localize the script with AJAX object for document stats
        wp_localize_script(
            'document-stats-script',
            'document_stats_ajax_object',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce')
            )
        );

        // Register and enqueue the text enhancer JavaScript
        wp_register_script(
            'text-enhancer-script',
            $this->plugin_url . 'assets/js/text-enhancer.js',
            array('jquery'),
            $this->version,
            true
        );

        // Register and enqueue the mega tooltip JavaScript
        wp_register_script(
            'mega-tooltip-script',
            $this->plugin_url . 'assets/js/mega-tooltip.js',
            array('jquery'),
            $this->version,
            true
        );

        // Register and enqueue the main document viewer JavaScript
        wp_register_script(
            'document-viewer-script',
            $this->plugin_url . 'assets/js/document-viewer.js',
            array('jquery', 'document-ocr-script', 'document-stats-script', 'text-enhancer-script', 'mega-tooltip-script'),
            $this->version,
            true
        );

        // Registra lo script di debug per COST_RATE (solo in modalità sviluppo)
        wp_register_script(
            'cost-rate-debug-script',
            $this->plugin_url . 'assets/js/cost-rate-debug.js',
            array('jquery', 'document-viewer-script', 'document-stats-script'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in JavaScript
        wp_register_script(
            'office-addin-script',
            $this->plugin_url . 'assets/js/office-addin.js',
            array('jquery', 'document-stats-script'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Preview JavaScript
        wp_register_script(
            'office-addin-preview-script',
            $this->plugin_url . 'assets/js/office-addin-preview.js',
            array('jquery', 'dashicons'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Editor JavaScript
        wp_register_script(
            'office-addin-editor-script',
            $this->plugin_url . 'assets/js/office-addin-editor.js',
            array('jquery', 'wp-color-picker', 'editor'),
            $this->version,
            true
        );

        // Register and enqueue the Office Add-in Preview CSS
        wp_register_style(
            'office-addin-preview-style',
            $this->plugin_url . 'assets/css/office-addin-preview.css',
            array('dashicons'),
            $this->version
        );

        // Register and enqueue the Office Add-in Editor CSS
        wp_register_style(
            'office-addin-editor-style',
            $this->plugin_url . 'assets/css/office-addin-editor.css',
            array('wp-color-picker', 'editor-buttons'),
            $this->version
        );

        // Localize the script with necessary data
        wp_localize_script(
            'document-viewer-script',
            'documentViewerParams',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce'),
                'pluginUrl' => $this->plugin_url,
                'i18n' => array(
'uploadError' => __('Error uploading file. Please try again.', 'document-viewer-plugin'),
                    'analysisError' => __('Error analyzing document. Please try again.', 'document-viewer-plugin'),
                    'exportError' => __('Error exporting PDF. Please try again.', 'document-viewer-plugin'),
                    'clearConfirm' => __('Are you sure you want to clear the current document? All analysis will be lost.', 'document-viewer-plugin'),
                    'saving' => __('Saving...', 'document-viewer-plugin'),
                    'saved' => __('Saved', 'document-viewer-plugin'),
                    'saveError' => __('Error saving changes', 'document-viewer-plugin'),
                    'ocrProcessing' => __('OCR Processing in progress...', 'document-viewer-plugin'),
                    'ocrComplete' => __('OCR Completed Successfully', 'document-viewer-plugin'),
                    'ocrError' => __('Error during OCR processing', 'document-viewer-plugin'),
                    'statsError' => __('Error loading statistics', 'document-viewer-plugin'),
                    'refreshStats' => __('Refresh Stats', 'document-viewer-plugin'),
                    'statsLoading' => __('Loading statistics...', 'document-viewer-plugin'),
                    'noAnalyses' => __('No analyses available', 'document-viewer-plugin'),
                )
            )
        );

        wp_enqueue_script('document-ocr-script');
        wp_enqueue_script('document-stats-script');
        wp_enqueue_script('text-enhancer-script');
        wp_enqueue_script('document-viewer-script');

        // Carica lo script di debug COST_RATE (solo in ambiente di sviluppo)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            wp_enqueue_script('cost-rate-debug-script');
        }

        // Register and enqueue the Document Analizer CSS
        wp_register_style(
            'document-analizer-style',
            $this->plugin_url . 'assets/css/widgets/document-analizer.css',
            array('document-viewer-style'),
            $this->version
        );
        wp_enqueue_style('document-analizer-style');

        // Register and enqueue the Document Analizer JavaScript  
        wp_register_script(
            'document-analizer-script',
            $this->plugin_url . 'assets/js/document-analizer.js',
            array('jquery', 'document-ocr-script', 'document-stats-script', 'text-enhancer-script', 'mega-tooltip-script'),
            $this->version,
            true
        );

        // Localize the Document Analizer script with necessary data
        wp_localize_script(
            'document-analizer-script',
            'documentAnalizerParams',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce'),
                'pluginUrl' => $this->plugin_url,
                'i18n' => array(
                    'uploadError' => __('Errore durante il caricamento del file. Riprova.', 'document-viewer-plugin'),
                    'analysisError' => __('Errore durante l\'analisi del documento. Riprova.', 'document-viewer-plugin'),
                    'exportError' => __('Errore durante l\'esportazione PDF. Riprova.', 'document-viewer-plugin'),
                    'clearConfirm' => __('Sei sicuro di voler cancellare il documento corrente? Tutta l\'analisi andrà persa.', 'document-viewer-plugin'),
                    'saving' => __('Salvataggio...', 'document-viewer-plugin'),
                    'saved' => __('Salvato', 'document-viewer-plugin'),
                    'saveError' => __('Errore nel salvataggio delle modifiche', 'document-viewer-plugin'),
                    'ocrProcessing' => __('Elaborazione OCR in corso...', 'document-viewer-plugin'),
                    'ocrComplete' => __('OCR completato con successo', 'document-viewer-plugin'),
                    'ocrError' => __('Errore durante l\'elaborazione OCR', 'document-viewer-plugin'),
                    'statsError' => __('Errore nel caricamento delle statistiche', 'document-viewer-plugin'),
                    'refreshStats' => __('Aggiorna Statistiche', 'document-viewer-plugin'),
                    'statsLoading' => __('Caricamento statistiche...', 'document-viewer-plugin'),
                    'noAnalyses' => __('Nessuna analisi disponibile', 'document-viewer-plugin'),
                )
            )
        );

        // Enqueue Document Analizer assets conditionally
        wp_enqueue_style('document-analizer-style');
        wp_enqueue_script('document-analizer-script');
    }

    public function render_document_viewer($atts) {
        // Carica il CSS document-stats solo quando il widget document viewer viene utilizzato
        wp_enqueue_style('document-stats-style',
            $this->plugin_url . 'assets/css/document-stats.css',
            array('document-viewer-style'),
            $this->version
        );

        // Verifica che l'utente sia loggato (WP o non WP)
        if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
            return '<div class="document-viewer-login-required">
                <p>' . __('Devi effettuare il login per accedere a questa funzionalità.', 'document-viewer-plugin') . '</p>
                <a href="' . site_url('/') . '" class="login-button">' . __('Accedi', 'document-viewer-plugin') . '</a>
            </div>';
        }

        $atts = shortcode_atts(array(
            'title' => __('Analisi Documento', 'document-viewer-plugin')
        ), $atts, 'document_viewer');

        ob_start();
        ?>
        <div class="document-viewer-widget">
            <!-- Colonna delle statistiche (sinistra) -->
            <div class="stats-column" id="stats-column">
                <h3><?php _e('Monitor activity', 'document-viewer-plugin'); ?></h3>

                <?php
                // Verifica se l'utente è un sottoscrittore esterno
                $is_subscriber = false;
                if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
                    $is_subscriber = fa_access_control()->is_current_user_subscriber();
                }

                // Mostra il messaggio di login solo se l'utente non è né un utente WordPress né un sottoscrittore
                if (!is_user_logged_in() && !$is_subscriber): ?>
                    <!-- Messaggio per utenti non loggati -->
                    <div class="login-required">
                        <p><?php _e('Effettua il login per visualizzare le tue statistiche', 'document-viewer-plugin'); ?></p>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>"><?php _e('Accedi', 'document-viewer-plugin'); ?></a>
                    </div>
                <?php else: ?>
                    <!-- Contenitore statistiche utente -->
                    <div id="user-stats-container" class="stats-section">
                        <div class="stats-section-header">
                            <span><?php _e('Dati Utilizzo', 'document-viewer-plugin'); ?></span>
                            <span class="toggle-icon expanded"></span>
                        </div>
                        <div class="stats-section-content">                            <!-- Info utente -->
                            <div class="user-info">
                                <div class="user-avatar" id="user-avatar"></div>
                                <div class="user-details">
                                    <div class="user-name" id="user-name">...</div>
                                    <div class="user-role" id="user-role">...</div>
                                </div>
                                <div class="stats-logout-button">
                                    <a href="<?php echo esc_url(wp_logout_url(get_permalink())); ?>" class="stats-logout-link" onclick="cleanUserDataBeforeLogout(event)">
                                        <span class="dashicons dashicons-exit"></span> Logout
                                    </a>
                                </div>
                            </div>

                            <!-- Statistiche in grid con wrapper container per CSS specificity -->
                            <div class="document-viewer-stats-container">
                                <div class="stats-grid">
                                    <div class="stats-row usage-row">
                                        <div class="stats-item">
                                            <div class="stats-label">
                                                <?php _e('Analisi', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Numero totale di analisi effettuate', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value" id="analyses-count">-</div>
                                        </div>
                                        <div class="stats-item">
                                            <div class="stats-label">
                                                <?php _e('Token', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Numero totale di token utilizzati in tutte le analisi', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value" id="tokens-count">-</div>
                                        </div>
                                    </div>

                                    <div class="stats-row costs-row">
                                        <div class="stats-item cost-item">
                                            <div class="stats-label">
                                                <?php _e('Spesa Stimata', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Stima del costo basata sul numero di token utilizzati', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value cost-highlight" id="cost-estimate">€0.00</div>
                                        </div>
                                        <div class="stats-item cost-item">
                                        <div class="stats-label">
                                                <?php _e('Spesa Effettiva', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip"><?php _e('Costo effettivo dell\'analisi completata', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value cost-highlight" id="actual-cost">€0.00</div>
                                        </div>
                                    </div>

                                    <!-- Costo totale nascosto - mantenuto solo nel database per statistiche -->
                                    <div class="stats-row total-cost-row" style="display: none;">
                                        <div class="stats-item total-cost-item">
                                            <div class="stats-label">
                                                <?php _e('Spesa Totale', 'document-viewer-plugin'); ?>
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip><?php _e('Costo totale di tutte le analisi (dato statistico)', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value total-cost-highlight" id="tot-cost">€0.00</div>
                                        </div>
                                    </div>

                                    <div class="stats-row credit-row">
                                        <div class="stats-item credit-item">
                                        <div class="stats-label">
                                                <?php _e('Credito Disponibile', 'document-viewer-plugin'); ?>
                                                
                                                
                                                <span class="stats-info-icon">i</span>
                                                <div class="stats-tooltip>
                                                <?php _e('Credito disponibile per l\'esecuzione di nuove analisi', 'document-viewer-plugin'); ?></div>
                                            </div>
                                            <div class="stats-value credit-highlight" id="credits-available">€0.00</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Rimossa sezione date prima e ultima analisi -->
                        </div>
                    </div>

                    <!-- Analisi recenti -->
                    <div id="recent-analyses-container" class="stats-section">
                        <div class="stats-section-header">
                            <span><?php _e('Analisi Recenti', 'document-viewer-plugin'); ?></span>
                            <span class="toggle-icon expanded"></span>
                        </div>
                        <div class="stats-section-content">
                            <div id="recent-analyses-list" class="recent-analyses-list">
                                <!-- Le analisi verranno caricate dinamicamente -->
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Colonna del form (centro) -->
        <div class="document-form-column">
                <h3><?php _e('Carica e Analizza', 'document-viewer-plugin'); ?></h3>

                <form id="document-viewer-form" enctype="multipart/form-data">
                    <!-- Custom logo upload - SPOSTATO IN ALTO -->
                    <div class="form-row custom-logo-field">
                        <input type="file" id="custom-logo-upload" accept="image/*" />
                        <label for="custom-logo-upload"><?php _e('Scegli Logo', 'document-viewer-plugin'); ?></label>
                        <label for="custom-logo-upload" class="logo-description"><?php _e('Logo Personalizzato (opzionale)', 'document-viewer-plugin'); ?></label>
                        <div class="logo-preview-container">
                            <img id="logo-preview" src="" alt="Logo Preview" style="display: none;">
                            <div id="logo-dimensions-info" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- Titolo analisi - SPOSTATO SUBITO DOPO IL LOGO -->
                    <div class="form-row">
                        <label for="analysis-title"><?php _e('Titolo Analisi:', 'document-viewer-plugin'); ?></label>
                        <input type="text" id="analysis-title" placeholder="<?php _e('Inserisci un titolo per l\'analisi', 'document-viewer-plugin'); ?>" />
                    </div>

                    <!-- File upload section - SPOSTATO DOPO IL TITOLO -->
                    <div class="form-row file-upload-row">
                        <label for="document-upload"><?php _e('Carica Documento (PDF, Word o Immagine):', 'document-viewer-plugin'); ?></label>
                        <div class="file-upload-container">
                            <div class="file-upload-input">
                                <input type="file" id="document-upload" accept=".pdf,.doc,.docx" />
                                <label for="document-upload"><?php _e('Scegli PDF/Word', 'document-viewer-plugin'); ?></label>

                                <!-- Campo per il caricamento delle immagini -->
                                <input type="file" id="image-upload" accept="image/*" />
                                <label for="image-upload"><?php _e('Scegli Immagine', 'document-viewer-plugin'); ?></label>
                            </div>
                            <div id="document-info-inline" class="document-info-inline" style="display: none;">
                                <span id="document-name-inline"></span>
                                <span id="document-size-inline"></span>
                                <span id="document-type-inline"></span>
                                <span id="document-chars-inline"></span>
                                <span id="document-tokens-inline"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Descrizione/domanda -->
                    <div class="form-row">
                        <label for="document-description"><?php _e('Descrizione o Domanda:', 'document-viewer-plugin'); ?></label>

                        <!-- Menu a tendina per richieste predefinite -->
                        <div class="preset-queries-container">
                            <select id="preset-queries" class="preset-queries-select">
                                <option value=""><?php _e('-- Seleziona una richiesta predefinita --', 'document-viewer-plugin'); ?></option>
                                <?php
                                // Recupera le richieste predefinite dal database
                                $preset_queries = $this->get_preset_queries();
                                foreach ($preset_queries as $query) {
                                    echo '<option value="' . esc_attr($query['query_text']) . '">' . esc_html($query['title']) . '</option>';
                                }
                                ?>
                            </select>
                            <span class="preset-info-tip" title="<?php _e('Seleziona una richiesta predefinita o scrivi la tua domanda personalizzata', 'document-viewer-plugin'); ?>">?</span>
                        </div>

                        <textarea id="document-description" rows="4"
                                placeholder="<?php _e('Inserisci una domanda o descrivi cosa vuoi analizzare nel documento', 'document-viewer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-row">
                        <button type="button" id="analyze-description"><?php _e('Analizza', 'document-viewer-plugin'); ?></button>
                        <button type="button" id="clear-document"><?php _e('Cancella', 'document-viewer-plugin'); ?></button>
                    </div>

                    <!-- Note/Annotations section -->
                    <div class="form-row">
                        <label for="document-annotations"><?php _e('Note Aggiuntive (opzionale):', 'document-viewer-plugin'); ?></label>
                        <textarea id="document-annotations" rows="3"
                                placeholder="<?php _e('Eventuali note da includere nel report', 'document-viewer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Export actions -->
                    <div class="export-actions">
                        <button type="button" id="export-pdf"><?php _e('Esporta in PDF', 'document-viewer-plugin'); ?></button>
                        <button type="button" id="save-analysis"><?php _e('Salva Analisi', 'document-viewer-plugin'); ?></button>
                        <div id="save-result-message" class="save-result-message"></div>
                    </div>
                </form>
            </div>

            <!-- Colonna di visualizzazione (destra) -->
            <div class="document-display-column">
                <!-- Analysis results - SPOSTATO IN ALTO -->
                <h3><?php _e('', 'document-viewer-plugin'); ?></h3>
                <div id="analysis-results">
                <div style="background-image: url('<?php echo esc_url(plugins_url('/financial-advisor-V4/images/bg-da.jpg', dirname(__FILE__))); ?>'); background-size: cover; background-position: center; padding: 40px 20px; border-radius: 8px; min-height: 150px; display: flex; align-items: center; padding: 30px; border-radius: 8px; max-width: 800px;">
                <div style="padding: 20px; border-radius: 8px; max-width: 600px;">
                <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                  <?php _e('Carica un documento', 'document-viewer-plugin'); ?>
                            </h5><p>
                        <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                        <?php _e('per iniziare l\'analisi', 'document-viewer-plugin'); ?>
                       </h5></p>
                   <h5 style="text-align: left; margin: 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                    <?php _e('con supporto AI Financial Expert', 'document-viewer-plugin'); ?>
                  </h5>
               </div>
               </div>
                </div>

                <!-- Document display - SPOSTATO IN BASSO -->
                <h3><?php _e('Visualizzazione Documento', 'document-viewer-plugin'); ?></h3>

                <!-- Status notification area -->
                <div id="document-notification-area" class="document-notification-area" style="display: none;">
                    <div class="notification-content"></div>
                </div>

                <!-- Zoom controls -->
                <div class="zoom-controls">
                    <button type="button" id="zoom-in" class="zoom-btn" style="display: none;">+</button>
                    <button type="button" id="zoom-out" class="zoom-btn" style="display: none;">-</button>
                </div>

                <div id="document-display" style="display: none;">
                    <iframe id="document-frame" style="display: none;"></iframe>
                </div>


            </div>

        </div> <!-- End document-viewer-widget -->
        <?php
        return ob_get_clean();
    }

    public function render_chat_viewer() {
        // Verifica che l'utente sia loggato
        if (!is_user_logged_in()) {
            return '<div class="document-viewer-login-required">
                <p>' . __('Devi effettuare il login per accedere a questa funzionalità.', 'document-viewer-plugin') . '</p>
                <a href="' . site_url('/') . '" class="login-button">' . __('Accedi', 'document-viewer-plugin') . '</a>
            </div>';
        }

        // Get API settings that will be needed for the chat
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        ob_start();
        ?>
        <div class="chat-widget"
             data-api-endpoint="<?php echo esc_attr($api_endpoint); ?>"
             data-api-model="<?php echo esc_attr($api_model); ?>">
            <h3><?php _e('Chat with Model', 'document-viewer-plugin'); ?></h3>
            <div class="chat-form">
                <div class="chat-log"></div>
                <div class="input-wrapper">
                    <input type="text" class="chat-input" placeholder="<?php _e('Enter your message...', 'document-viewer-plugin'); ?>" />
                    <button type="button" class="send-btn" aria-label="<?php _e('Send Message', 'document-viewer-plugin'); ?>"></button>
                </div>
                <div class="chat-controls">
                    <button type="button" class="send-chat"><?php _e('Send', 'document-viewer-plugin'); ?></button>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public function clear_document() {
        check_ajax_referer('document_viewer_nonce', 'nonce');
        wp_send_json_success(array('message' => __('Document cleared successfully.', 'document-viewer-plugin')));
    }

    public function upload_document() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_die(__('Devi effettuare il login per caricare documenti.', 'document-viewer-plugin'));
            return;
        }

        // Rest of the function remains the same
    }

    public function analyze_document() {
        // Check nonce for security - ma non interrompere l'esecuzione in caso di errore
        // per consentire agli utenti non WordPress di utilizzare la funzionalità
        $nonce_verified = false;

        try {
            $nonce_verified = check_ajax_referer('document_viewer_nonce', 'nonce', false);
            if (!$nonce_verified) {
                dv_debug_log('Nonce verification failed but continuing for non-WordPress users', 'analysis');
            }
        } catch (Exception $e) {
            dv_debug_log('Exception during nonce verification: ' . $e->getMessage(), 'analysis');
        }

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        // Ma solo se il nonce è stato verificato (per utenti WordPress)
        if ($nonce_verified && !is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Devi effettuare il login per utilizzare questa funzionalità.', 'document-viewer-plugin')]);
            return;
        }

        // Check for developer mode
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');
        $description = isset($_POST['description']) ? $_POST['description'] : '';

        // If developer mode is enabled, description is provided, and we're trying to analyze (not just extract content)
        if ($developer_mode && !empty($developer_response) && !empty($description)) {
            dv_debug_log('Developer mode active - returning custom response for document analysis', 'analysis');

            // Format the response just like normal API response, including the HTML structure with edit buttons
            // but with a specific class for developer mode
            $formatted_html = '';
            $lines = explode("\n", $developer_response);

            foreach ($lines as $line) {
                // Convert multiple spaces to &nbsp; entities to preserve spacing
                $line = preg_replace('/  /', '&nbsp;&nbsp;', $line);

                // Add paragraph tags for better structure instead of just line breaks
                if (trim($line) !== '') {
                    $formatted_html .= '<p>' . htmlspecialchars($line) . '</p>';
                } else {
                    $formatted_html .= '<p>&nbsp;</p>'; // Empty paragraph for spacing
                }
            }

            $formatted_response = '
                <div id="analysis-content" class="developer-content">' . $formatted_html . '</div>
            ';

            wp_send_json_success(array(
                'results' => $formatted_response,
                'message' => __('Analysis completed in developer mode.', 'document-viewer-plugin')
            ));
            return;
        }

        // Check PDF Parser availability first
        if (!$this->check_pdf_parser()) {
            dv_debug_log('PDF Parser not available - check your composer dependencies', 'analysis');
            wp_send_json_error(array('message' => __('PDF Parser not available. Please contact administrator.', 'document-viewer-plugin')));
            return;
        }

        // Get upload directory - handle both WordPress and non-WordPress users
        if (function_exists('wp_upload_dir')) {
            $upload_dir = wp_upload_dir();
            $plugin_upload_dir = $upload_dir['basedir'] . '/document-viewer';
        } else {
            // Fallback for non-WordPress users
            $plugin_upload_dir = dirname(__FILE__) . '/uploads/document-viewer';

            // Make sure the uploads directory exists for non-WordPress users
            if (!file_exists(dirname(__FILE__) . '/uploads')) {
                @mkdir(dirname(__FILE__) . '/uploads', 0755, true);
            }

            // Ensure the document-viewer directory exists with correct permissions
            if (!file_exists($plugin_upload_dir)) {
                @mkdir($plugin_upload_dir, 0755, true);
                dv_debug_log("Created non-WordPress upload directory: $plugin_upload_dir", 'analysis');
            }
        }

        // Log permissions for debugging
        dv_debug_log("Upload directory: $plugin_upload_dir");
        dv_debug_log("Upload directory exists: " . (file_exists($plugin_upload_dir) ? 'yes' : 'no'));
        dv_debug_log("Upload directory writable: " . (is_writable($plugin_upload_dir) ? 'yes' : 'no'));

        $document_data = isset($_POST['document_data']) ? $_POST['document_data'] : '';
        $description = isset($_POST['description']) ? $_POST['description'] : '';
        $save_content = isset($_POST['save_content']) ? (bool)$_POST['save_content'] : false;

        $extracted_content = '';

        // Handle file upload - only extract content, don't analyze
        if (isset($_FILES['document_file'])) {
            try {
                $file = $_FILES['document_file'];
                dv_debug_log("Received file upload: " . print_r($file, true));

                if ($file['error'] !== UPLOAD_ERR_OK) {
                    $error_message = $this->get_upload_error_message($file['error']);
                    dv_debug_log("File upload error: " . $error_message, 'analysis');
                    throw new Exception($error_message);
                }

                // Verify temp file exists and is readable
                if (!is_readable($file['tmp_name'])) {
                    dv_debug_log("Uploaded file is not readable: " . $file['tmp_name'], 'analysis');
                    throw new Exception('Uploaded file is not readable.');
                }

                $mime_type = $file['type'];
                $temp_file = $file['tmp_name'];

                // Log file details
                dv_debug_log("File details - Name: " . $file['name'] . ", Type: " . $mime_type . ", Size: " . $file['size'] . " bytes", 'analysis');

                // Make sure upload directory exists
                if (!file_exists($plugin_upload_dir)) {
                    dv_debug_log("Creating upload directory: " . $plugin_upload_dir, 'analysis');
                    // Usa mkdir invece di wp_mkdir_p per supportare anche utenti non WordPress
                    if (!function_exists('wp_mkdir_p') || !wp_mkdir_p($plugin_upload_dir)) {
                        // Fallback per utenti non WordPress
                        if (!@mkdir($plugin_upload_dir, 0755, true)) {
                            dv_debug_log("Failed to create upload directory", 'analysis');
                            throw new Exception('Could not create upload directory.');
                        }
                    }
                }

                // Move file to plugin directory temporarily
                // Usa una funzione custom per sanitizzare il nome del file se sanitize_file_name non è disponibile
                $safe_filename = function_exists('sanitize_file_name') ?
                    sanitize_file_name($file['name']) :
                    preg_replace('/[^a-zA-Z0-9_.-]/', '_', $file['name']);

                // Add a unique prefix to avoid filename collisions
                $safe_filename = uniqid('doc_') . '_' . $safe_filename;

                $temp_dest = $plugin_upload_dir . '/' . $safe_filename;
                if (!move_uploaded_file($temp_file, $temp_dest)) {
                    dv_debug_log("Failed to move uploaded file to: " . $temp_dest, 'analysis');
                    throw new Exception('Failed to move uploaded file.');
                }

                dv_debug_log("File moved successfully to: $temp_dest");
                dv_debug_log("File exists at destination: " . (file_exists($temp_dest) ? 'yes' : 'no'));
                dv_debug_log("File is readable: " . (is_readable($temp_dest) ? 'yes' : 'no'));
                dv_debug_log("File size: " . filesize($temp_dest) . " bytes");

                // Process the file based on type
                switch ($mime_type) {
                    case 'application/pdf':
                        dv_debug_log("Avvio estrazione contenuto da file PDF", 'analysis');
                        if (!$this->check_pdf_parser()) {
                            dv_debug_log("PDF Parser not available", 'analysis');
                            throw new Exception('PDF Parser not available');
                        }
                        $extracted_content = $this->extract_pdf_content($temp_dest);
                        dv_debug_log("PDF extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    case 'application/msword':
                        dv_debug_log("Avvio estrazione contenuto da documento Word", 'analysis');
                        if (!$this->check_phpword()) {
                            dv_debug_log("PHPWord library not available", 'analysis');
                            throw new Exception('PHPWord library not available');
                        }
                        $extracted_content = $this->extract_docx_content($temp_dest);
                        dv_debug_log("Word extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    case 'text/plain':
                        dv_debug_log("Avvio estrazione contenuto da file di testo", 'analysis');
                        $extracted_content = file_get_contents($temp_dest);
                        $extracted_content = $this->clean_extracted_text($extracted_content);
                        dv_debug_log("Text extraction complete. Content length: " . strlen($extracted_content), 'analysis');
                        break;

                    default:
                        dv_debug_log("Unsupported file type: " . $mime_type, 'analysis');
                        throw new Exception('Unsupported file type: ' . $mime_type);
                }

                // Cleanup
                if (file_exists($temp_dest)) {
                    unlink($temp_dest);
                    dv_debug_log("Temporary file removed", 'analysis');
                }

                if ($save_content) {
                    dv_debug_log("Content extracted successfully, length: " . strlen($extracted_content));
                    // Return only the extracted content without analysis when save_content is true
                    wp_send_json_success(array(
                        'document_content' => $extracted_content,
                        'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
                    ));
                    return;
                }

            } catch (Exception $e) {
                dv_debug_log('Document processing error: ' . $e->getMessage(), 'analysis');
                wp_send_json_error(array('message' => 'Error processing document: ' . $e->getMessage()));
                return;
            }
        }

        // If we get here and have no description, it means we're not supposed to analyze yet
        if (empty($description)) {
            // If we have extracted content but no description, just return the content without analysis
            if (!empty($extracted_content)) {
                dv_debug_log("Fase di estrazione completata. Contenuto estratto: " . strlen($extracted_content) . " caratteri. In attesa di richiesta di analisi.", 'analysis');
                wp_send_json_success(array(
                    'document_content' => $extracted_content,
                    'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
                ));
                return;
            }

            // If no description and no content, it's an error
            if (empty($document_data)) {
                dv_debug_log("No document content or description provided", 'analysis');
                wp_send_json_error(array('message' => __('No document content or description provided.', 'document-viewer-plugin')));
                return;
            }
        }

        // Use either extracted content or posted document data
        $content_to_analyze = $extracted_content ?: $document_data;

        if (empty($content_to_analyze)) {
            dv_debug_log("No document content available for analysis", 'analysis');
            wp_send_json_error(array('message' => __('No document content available for analysis.', 'document-viewer-plugin')));
            return;
        }

        // Only proceed with analysis if we have a description (indicating the Analyze button was clicked)
        if (!empty($description)) {
            // Get Analysis API settings
            $api_key = get_option('document_viewer_api_key');
            $api_endpoint = get_option('document_viewer_api_endpoint');
            $api_model = get_option('document_viewer_model');

            // If Analysis API settings are not set, try using primary API settings
            if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
                dv_debug_log("Analysis API settings not found, using primary API settings", 'analysis');
            }

            // Check if we have the necessary API settings
            if (empty($api_key)) {
                dv_debug_log("API key is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API key is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            if (empty($api_endpoint)) {
                dv_debug_log("API endpoint is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API endpoint is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            if (empty($api_model)) {
                dv_debug_log("API model is not configured for analysis", 'analysis');
                wp_send_json_error(array('message' => __('API model is not configured. Please check settings.', 'document-viewer-plugin')));
                return;
            }

            // Ensure the endpoint points to the chat completions endpoint
            $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
            dv_debug_log("FASE DI ANALISI: inizio elaborazione con modello AI", 'analysis');
            dv_debug_log("Invio richiesta di analisi a: " . $api_url, 'analysis');
            dv_debug_log("Utilizzo modello: " . $api_model, 'analysis');

            // Set up prompt
            $system_prompt = "You are an expert financial document analyzer. Analyze the provided document content and respond with a detailed breakdown addressing the user's specific question or request. Format your response in clear paragraphs with appropriate headings.";

            // Structure the request body according to OpenRouter's expectations
            $request_body = array(
                'model' => $api_model,
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => $system_prompt
                    ),
                    array(
                        'role' => 'user',
                        'content' => "Here is the document content to analyze:\n\n" . $content_to_analyze . "\n\nUser's request: " . $description
                    )
                ),
                'temperature' => 0.3,
                'max_tokens' => 4000
            );

            // Headers for the request
            $headers = array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => site_url(), // Required by OpenRouter
                'X-Title' => 'Document Viewer Plugin', // Identify app to OpenRouter
                'Accept' => 'application/json'
            );

            $request_args = array(
                'headers' => $headers,
                'body' => wp_json_encode($request_body),
                'method' => 'POST',
                'timeout' => 120, // Longer timeout for analysis
                'data_format' => 'body',
                'sslverify' => true,
                'redirection' => 5,
                'httpversion' => '1.1',
                'blocking' => true,
                'cookies' => array()
            );

            dv_debug_log("FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...", 'analysis');

            // Make the request
            $response = wp_remote_post($api_url, $request_args);

            // Check for errors
            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                dv_debug_log("FASE DI ANALISI: errore nella richiesta API: " . $error_message, 'analysis');
                wp_send_json_error(array('message' => __('API request failed: ', 'document-viewer-plugin') . $error_message));
                return;
            }

            // Get response code and body
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            dv_debug_log("FASE DI ANALISI: risposta API ricevuta, codice: " . $status_code, 'analysis');
            dv_debug_log("FASE DI ANALISI: inizio elaborazione risposta", 'analysis');

            // Parse the response
            $data = json_decode($body, true);

            // Check for error responses
            if ($status_code !== 200) {
                $error_message = isset($data['error']) && isset($data['error']['message'])
                    ? $data['error']['message']
                    : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

                dv_debug_log("FASE DI ANALISI: errore API: " . $error_message, 'analysis');
                wp_send_json_error(array('message' => $error_message));
                return;
            }

            // Extract the analysis result from the response
            $analysis_content = $this->extract_analysis_result($data);

            if (empty($analysis_content)) {
                dv_debug_log("FASE DI ANALISI: impossibile estrarre il contenuto dell'analisi dalla risposta API", 'analysis');
                wp_send_json_error(array('message' => __('Could not extract analysis from API response.', 'document-viewer-plugin')));
                return;
            }

            // Format the response to HTML for display
            $formatted_html = '';
            $lines = explode("\n", $analysis_content);

            foreach ($lines as $line) {
                if (trim($line) !== '') {
                    $formatted_html .= '<p>' . htmlspecialchars($line) . '</p>';
                } else {
                    $formatted_html .= '<p>&nbsp;</p>'; // Empty paragraph for spacing
                }
            }

            $formatted_response = '
                <div id="analysis-content">' . $formatted_html . '</div>
            ';

            dv_debug_log("FASE DI ANALISI: elaborazione completata con successo.", 'analysis');

            wp_send_json_success(array(
                'results' => $formatted_response,
                'message' => __('Analysis completed successfully.', 'document-viewer-plugin')
            ));
        } else {
            // If there's no description but we have content, just return the content
            dv_debug_log("Testo estratto correttamente. In attesa di richiesta di analisi.", 'analysis');
            wp_send_json_success(array(
                'document_content' => $content_to_analyze,
                'message' => __('Document content extracted successfully.', 'document-viewer-plugin')
            ));
        }
    }

    public function chat_model() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(array('message' => __('Devi effettuare il login per utilizzare la chat.', 'document-viewer-plugin')));
            return;
        }

        // Check for developer mode
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');

        if ($developer_mode && !empty($developer_response)) {
            dv_debug_log('Developer mode active - returning custom response instead of API call', 'primary');
            wp_send_json_success(array('reply' => $developer_response));
            return;
        }

        // Verifica configurazione API
        $api_key = get_option('document_viewer_api_key');
        // Ensure the endpoint always points to the chat completions endpoint
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';
        $api_model = get_option('document_viewer_model');

        if (empty($api_key)) {
            wp_send_json_error(array('message' => __('API key is not configured', 'document-viewer-plugin')));
            return;
        }

        if (empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API endpoint is not configured', 'document-viewer-plugin')));
            return;
        }

        if (empty($api_model)) {
            wp_send_json_error(array('message' => __('API model is not configured', 'document-viewer-plugin')));
            return;
        }

        $chat_message = isset($_POST['chat_message']) ? sanitize_text_field($_POST['chat_message']) : '';
        if (empty($chat_message)) {
            wp_send_json_error(array('message' => __('Message is required', 'document-viewer-plugin')));
            return;
        }

        if (strtolower(trim($chat_message)) === 'ping') {
            wp_send_json_success(array('reply' => 'Pong! Connection is working.'));
            return;
        }

        // Get chat history if provided
        $chat_history = isset($_POST['chat_history']) ? $_POST['chat_history'] : '';
        $messages = array();

        // Add system message
        $messages[] = array(
            'role' => 'system',
            'content' => 'You are an assistant that helps users with financial document analysis and questions. Be concise and helpful.'
        );

        // Add chat history if provided
        if (!empty($chat_history)) {
            $history_messages = json_decode(stripslashes($chat_history), true);
            if (is_array($history_messages)) {
                $messages = array_merge($messages, $history_messages);
            }
        }

        // Add current user message
        $messages[] = array(
            'role' => 'user',
            'content' => $chat_message
        );

        // Set up the request body
        $request_body = array(
            'model' => $api_model,
            'messages' => $messages,
            'temperature' => 0.7,
            'max_tokens' => 800
        );

        // Headers for the request
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(), // Required by OpenRouter
            'X-Title' => 'Document Viewer Plugin', // Identify app to OpenRouter
            'Accept' => 'application/json'
        );

        // Request args
        $args = array(
            'headers' => $headers,
            'body' => wp_json_encode($request_body),
            'method' => 'POST',
            'timeout' => 60,
            'data_format' => 'body',
            'sslverify' => true,
            'redirection' => 5,
            'httpversion' => '1.1',
            'blocking' => true
        );

        dv_debug_log('Sending chat message to API: ' . $chat_message, 'chat');

        // Make the API request
        $response = wp_remote_post($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            dv_debug_log('Chat API request error: ' . $error_message, 'chat');
            wp_send_json_error(array('message' => __('API request failed: ', 'document-viewer-plugin') . $error_message));
            return;
        }

        // Get response code and body
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        dv_debug_log('Chat API response received, status code: ' . $status_code, 'chat');

        // Parse the response
        $data = json_decode($body, true);

        // Check for error responses
        if ($status_code !== 200) {
            $error_message = isset($data['error']) && isset($data['error']['message'])
                ? $data['error']['message']
                : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

            dv_debug_log('Chat API error: ' . $error_message, 'chat');
            wp_send_json_error(array('message' => $error_message));
            return;
        }

        // Extract the AI reply from the response
        $ai_reply = '';
        if (isset($data['choices']) && is_array($data['choices']) && !empty($data['choices'])) {
            $choice = $data['choices'][0];
            if (isset($choice['message']) && isset($choice['message']['content'])) {
                // ChatGPT/GPT-4 format
                $ai_reply = $choice['message']['content'];
            } else if (isset($choice['text'])) {
                // Older GPT-3 format
                $ai_reply = $choice['text'];
            }
        }

        if (empty($ai_reply)) {
            dv_debug_log('Could not extract AI reply from API response', 'chat');
            wp_send_json_error(array('message' => __('Could not extract reply from API response.', 'document-viewer-plugin')));
            return;
        }

        dv_debug_log('Chat AI reply extracted successfully, length: ' . strlen($ai_reply), 'chat');

        // Return success response with the AI reply
        wp_send_json_success(array('reply' => $ai_reply));
    }    public function register_widget() {
        register_widget('Document_Viewer_Widget');
        register_widget('Chat_Model_Widget'); // Registra il nuovo widget
        register_widget('Document_Analizer_Widget'); // Registra il nuovo Document Analizer Widget
    }

    public function export_analysis_pdf() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        if (!$this->check_mpdf()) {
            wp_send_json_error(['message' => 'mPDF not available']);
            return;
        }

        try {
            // Get selected sections flags (new parameters)
            $include_query = isset($_POST['include_query']) ? ($_POST['include_query'] === '1' || $_POST['include_query'] === 'true') : true;
            $include_full_analysis = isset($_POST['include_full_analysis']) ? ($_POST['include_full_analysis'] === '1' || $_POST['include_full_analysis'] === 'true') : true;
            $include_key_points = isset($_POST['include_key_points']) ? ($_POST['include_key_points'] === '1' || $_POST['include_key_points'] === 'true') : true;
            $include_original_doc = isset($_POST['include_original_doc']) ? ($_POST['include_original_doc'] === '1' || $_POST['include_original_doc'] === 'true') : true;

            // Receive HTML content directly - supports both 'analysis_html' (new) and 'analysis' (old)
            $analysis_html = isset($_POST['analysis_html']) ? $_POST['analysis_html'] : (isset($_POST['analysis']) ? $_POST['analysis'] : '');
            $description = isset($_POST['description']) ? sanitize_textarea_field($_POST['description']) : '';
            $annotations = isset($_POST['annotations']) ? sanitize_textarea_field($_POST['annotations']) : '';
            $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : __('Document Analysis Report', 'document-viewer-plugin');
            $uploaded_pdf = isset($_FILES['uploaded_pdf']) ? $_FILES['uploaded_pdf'] : null;
            $custom_logo = isset($_FILES['custom_logo']) ? $_FILES['custom_logo'] : null;

            // Log for debugging
            dv_debug_log('export_analysis_pdf called with the following parameters:');
            dv_debug_log('- include_query: ' . ($include_query ? 'yes' : 'no'));
            dv_debug_log('- include_full_analysis: ' . ($include_full_analysis ? 'yes' : 'no'));
            dv_debug_log('- include_key_points: ' . ($include_key_points ? 'yes' : 'no'));
            dv_debug_log('- include_original_doc: ' . ($include_original_doc ? 'yes' : 'no'));
            dv_debug_log('- using analysis_html param: ' . (isset($_POST['analysis_html']) ? 'yes' : 'no'));
            dv_debug_log('- using analysis param: ' . (isset($_POST['analysis']) ? 'yes' : 'no'));
            dv_debug_log('- content length: ' . strlen($analysis_html));
            dv_debug_log('- is_preview: ' . (isset($_POST['preview_mode']) ? $_POST['preview_mode'] : 'false'));

            // Check if it's a preview request
            $is_preview = isset($_POST['preview_mode']) && $_POST['preview_mode'] === 'true';

            // Create custom mpdf configuration for better document layout and improved image handling
            $mpdf = new \Mpdf\Mpdf([
                'format' => 'A4',
                'margin_left' => 20,
                'margin_right' => 20,
                'margin_top' => 20,
                'margin_bottom' => 20,
                'margin_header' => 10,
                'margin_footer' => 10,
                'simpleTables' => true,
                'use_kwt' => true, // Keep text with tables
                'keep_table_proportions' => true,
                'img_dpi' => 300, // Higher DPI for better image quality
                'dpi' => 96, // Set DPI for overall PDF resolution
                'tempDir' => sys_get_temp_dir(), // Specify temp directory
                'watermarkImgBehind' => false,
                'adjustFontDescLineheight' => 1.5, // Better line height ratio
                'interpolateImages' => true, // Better image rendering
                'useSubstitutions' => true, // Allow font substitutions for better appearance
                'autoLangToFont' => true, // Automatic language to font mapping
                'showWatermarkImage' => true // Ensure watermarks and images are visible
            ]);

            // Set better font and line height for improved readability
            $mpdf->SetDefaultBodyCSS('font-family', 'Arial, sans-serif');
            $mpdf->SetDefaultBodyCSS('line-height', 1.6);

            // Enable embedding external images (crucial for ensuring images are included)
            $mpdf->curlAllowUnsafeSslRequests = true;
            $mpdf->showImageErrors = true;

            // Create CSS to support modern web styling
            $css = file_get_contents(plugin_dir_path(__FILE__) . 'assets/css/document-viewer.css');

            // Import the document's CSS for styling (or use defaults)
            $mpdf->WriteHTML('
            <style>
                /* Base styles for PDF document */
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    color: #333;
                    font-size: 11pt;
                }

                /* Header styling */
                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    page-break-inside: avoid;
                }

                .logo {
                    max-height: 60px;
                    max-width: 200px;
                    display: block;
                }

                .title {
                    color: #2c3e50;
                    text-align: center;
                    font-size: 24px;
                    margin: 0;
                    flex-grow: 1;
                    font-weight: bold;
                }

                .date {
                    text-align: right;
                    font-size: 12px;
                    color: #7f8c8d;
                }

                /* Separator line */
                .separator {
                    border: none;
                    border-top: 2px solid #3498db;
                    margin: 15px 0 25px 0;
                }

                /* Section styling */
                .section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #f5f9fc;
                    border-left: 4px solid #3498db;
                    page-break-inside: avoid;
                    border-radius: 4px;
                }

                .analysis-section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-left: 4px solid #2ecc71;
                    border-radius: 4px;
                }

                .notes-section {
                    margin: 20px 0;
                    padding: 15px;
                    background: #fff7f0;
                    border-left: 4px solid #e67e22;
                    page-break-inside: avoid;
                    border-radius: 4px;
                }

                /* Heading styles */
                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50;
                    margin-top: 0;
                    page-break-after: avoid;
                }

                /* Analysis content styling */
                .analysis-content {
                    color: #34495e;
                    font-family: Arial, sans-serif;
                    font-size: 11pt;
                    line-height: 1.5;
                    background-color: #fcfcfc;
                    padding: 15px;
                    border-radius: 4px;
                    border: 1px solid #eaeaea;
                    margin-top: 10px;
                }

                /* Charts and graphics styling */
                .chart, .graph {
                    max-width: 100%;
                    margin: 15px auto;
                    display: block;
                    page-break-inside: avoid;
                }

                /* Lists and bullet points styling */
                ul, ol {
                    padding-left: 20px;
                    margin-bottom: 15px;
                }

                ul li, ol li {
                    margin-bottom: 5px;
                    line-height: 1.5;
                }

                /* Improved table styling */
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 15px 0;
                    page-break-inside: avoid;
                }

                table, th, td {
                    border: 1px solid #ddd;
                }

                th {
                    background-color: #f2f2f2;
                    padding: 8px;
                    font-weight: bold;
                    text-align: left;
                }

                td {
                    padding: 8px;
                    vertical-align: top;
                }

                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }

                /* Page break control */
                .page-break {
                    page-break-before: always;
                }

                /* Image styling */
                img {
                    max-width: 100%;
                    height: auto !important;
                    display: block;
                    margin: 10px auto;
                }

                /* Code block styling */
                pre, code {
                    background-color: #f8f8f8;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 8px;
                    font-family: Consolas, Monaco, monospace;
                    font-size: 10pt;
                    white-space: pre-wrap;
                    word-break: break-word;
                    overflow-x: hidden;
                }

                /* Key points styling */
                .key-points-list {
                    background-color: #fff7f0;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                }

                .key-points-list li {
                    margin-bottom: 8px;
                    line-height: 1.5;
                }

                /* Preserve analysis styles */
                .analysis-container {
                    position: relative;
                    margin: 15px 0;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #f9f9f9;
                    padding: 15px;
                }

                .analysis-title {
                    margin-top: 0;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #eee;
                }

                /* Analysis tabs styling */
                .analysis-tab {
                    padding: 8px 12px;
                    font-weight: 500;
                    border-right: 1px solid #e0e0e0;
                    margin-bottom: 0;
                }

                .analysis-tab.active {
                    background-color: #fff;
                    border-bottom: 2px solid #0073aa;
                }

                /* Make sure paragraphs have proper spacing */
                p {
                    margin-bottom: 10px;
                    line-height: 1.5;
                }

                /* Document info styling */
                .document-title {
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 20px 0;
                    color: #2c3e50;
                }

                /* Preserve additional styling from the document-viewer.css */
                ' . $css . '
            </style>
            ', \Mpdf\HTMLParserMode::HEADER_CSS);

            // Start building HTML content
            $html = '
            <div class="header">
            ';

            // Add logo if uploaded, otherwise use default logo
            if ($custom_logo && !empty($custom_logo['tmp_name'])) {
                try {
                    $logo_mime = mime_content_type($custom_logo['tmp_name']);
                    if (strpos($logo_mime, 'image/') === 0) {
                        $image_data = file_get_contents($custom_logo['tmp_name']);
                        if ($image_data) {
                            $logo_base64 = base64_encode($image_data);
                            $html .= '<div><img class="logo" src="data:' . $logo_mime . ';base64,' . $logo_base64 . '" alt="Logo" /></div>';
                        }
                    }
                } catch (Exception $e) {
                    dv_debug_log("Logo image error: " . $e->getMessage());
                }
            } else {
                               // Default logo or empty space
                $html .= '<div style="width:200px;"></div>';
            }

            // Add title
            $html .= '<h1 class="title">' . esc_html($title) . '</h1>';

            // Add date
            $html .= '<div class="date">' . date('d/m/Y') . '</div>';
            $html .= '</div>';

            // Add separator line
            $html .= '<hr class="separator" />';

            // Add the query/description if selected and present
            if ($include_query && !empty($description)) {
                $html .= '<div class="section">';
                $html .= '<h2>' . __('Analysis Request Detail', 'document-viewer-plugin') . '</h2>';
                $html .= '<p>' . nl2br(esc_html($description)) . '</p>';
                $html .= '</div>';
            }

            // Add the analysis results if selected and with proper HTML formatting
            if ($include_full_analysis && !empty($analysis_html)) {
                $html .= '<div class="analysis-section">';
                $html .= '<h2>' . __('Analysis Results', 'document-viewer-plugin') . '</h2>';
                $html .= '<div class="analysis-content">';

                // Handle HTML content with improved preservation of formatting

                // 1. Remove any script tags for security
                $analysis_html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $analysis_html);

                // 2. Remove the Edit Analysis button which shouldn't appear in PDFs
                $analysis_html = preg_replace('/<button[^>]*class="edit-analysis-btn"[^>]*>.*?<\/button>/is', '', $analysis_html);

                // 3. Process SVG content to ensure it's properly rendered
                $analysis_html = $this->process_svg_for_pdf($analysis_html);

                // 4. Fix relative URLs in images to absolute
                $site_url = site_url();
                $analysis_html = preg_replace('/src=["\'](\/[^"\']+)["\']/', 'src="' . $site_url . '$1"', $analysis_html);

                // 5. Ensure paragraphs have proper spacing and line breaks are preserved
                if (strpos($analysis_html, '<p') === false) {
                    $analysis_html = '<p>' . str_replace("\n", "</p>\n<p>", $analysis_html) . '</p>';
                }

                // 6. Remove # character from Analisi Completa section
                $analysis_html = str_replace('Analisi #Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('#Analisi Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('# Analisi Completa', 'Analisi Completa', $analysis_html);
                $analysis_html = str_replace('Analisi # Completa', 'Analisi Completa', $analysis_html);

                // Clean up markdown formatting characters
                $analysis_html = $this->clean_text_for_pdf_export($analysis_html);

                // Add the HTML content with preserved formatting
                $html .= $analysis_html;

                $html .= '</div>'; // Close analysis-content
                $html .= '</div>'; // Close analysis-section
            }

            // Get key points HTML if available and selected
            $key_points_html = isset($_POST['key_points_html']) ? $_POST['key_points_html'] : '';

            // Add the key points section if selected and available
            if ($include_key_points && !empty($key_points_html)) {
                // Add a page break between analysis and key points sections
                if ($include_full_analysis && !empty($analysis_html)) {
                    $html .= '<div class="page-break"></div>';
                }
                $html .= '<div class="analysis-section" style="margin-top:30px; border-left: 4px solid #e67e22;">';
                $html .= '<h2>' . __('Key Points', 'document-viewer-plugin') . '</h2>';
                $html .= '<div class="analysis-content" style="background-color: #fff7f0;">';

                // Remove any script tags for security
                $key_points_html = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $key_points_html);

                // Process SVG content in key points
                $key_points_html = $this->process_svg_for_pdf($key_points_html);

                // Fix relative URLs in images to absolute
                $key_points_html = preg_replace('/src=["\'](\/[^"\']+)["\']/', 'src="' . $site_url . '$1"', $key_points_html);

                // Maintain paragraph structure by preserving paragraph tags
                // If key points are in <li> elements, preserve them and add proper spacing
                if (strpos($key_points_html, '<li>') !== false) {
                    // If there's a <ul> or <ol> tag present, keep it as is
                    if (strpos($key_points_html, '<ul') === false && strpos($key_points_html, '<ol') === false) {
                        // If there's no list container, add it
                        $key_points_html = '<ul class="key-points-list" style="margin-left: 20px; line-height: 1.8;">' . $key_points_html . '</ul>';
                    }

                    // Make sure each list item has proper spacing
                    $key_points_html = preg_replace('/<\/li>\s*<li>/is', '</li>' . "\n" . '<li>', $key_points_html);
                } else {
                    // If it's not in a list format, make sure paragraphs are properly spaced
                    $key_points_html = preg_replace('/<\/p>\s*<p>/is', '</p>' . "\n" . '<p>', $key_points_html);

                    // If there are no paragraph tags at all, add them
                    if (strpos($key_points_html, '<p>') === false) {
                        $lines = explode("\n", strip_tags($key_points_html));
                        $key_points_html = '';
                        foreach ($lines as $line) {
                            if (trim($line) !== '') {
                                $key_points_html .= '<p>' . trim($line) . '</p>' . "\n";
                            }
                        }
                    }
                }

                // Clean up markdown formatting characters
                $key_points_html = $this->clean_text_for_pdf_export($key_points_html);

                // Add the HTML content with preserved formatting
                $html .= $key_points_html;

                $html .= '</div>'; // Close analysis-content
                $html .= '</div>'; // Close analysis-section
            }

            // Add annotations if present
            if (!empty($annotations)) {
                $html .= '<div class="notes-section">';
                $html .= '<h2>' . __('Additional Notes', 'document-viewer-plugin') . '</h2>';
                $html .= '<p>' . nl2br(esc_html($annotations)) . '</p>';
                $html .= '</div>';
            }

            // Flag to track if we've written the HTML to the PDF yet
            $html_written_during_pdf_import = false;

            // Add the original document on a new page if selected
            if ($include_original_doc && $uploaded_pdf && is_array($uploaded_pdf) && $uploaded_pdf['error'] === UPLOAD_ERR_OK) {
                // If the document is a PDF, import it directly
                if ($uploaded_pdf['type'] === 'application/pdf') {
                    try {
                        // Check if the file is readable
                        if (is_readable($uploaded_pdf['tmp_name'])) {
                            // Set the HTML we've created so far
                            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                            $html_written_during_pdf_import = true;

                            // Aggiungi una nuova pagina per il documento originale
                            $mpdf->AddPage();
                              // Aggiungi titolo centrato per la nuova pagina
                            $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                            // Add extra space between title and document content
                            $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                            // Now import the original PDF as additional pages
                            $pagecount = $mpdf->SetSourceFile($uploaded_pdf['tmp_name']);
                            dv_debug_log("PDF added with $pagecount pages");

                            // Import all pages of the original PDF
                            for ($i = 1; $i <= $pagecount; $i++) {
                                $tplId = $mpdf->ImportPage($i);

                                // Get the imported page's size
                                $pageSize = $mpdf->getTemplateSize($tplId);
                                $pageWidth = $pageSize['width'];
                                $pageHeight = $pageSize['height'];

                                // Posiziona il documento in alto a sinistra invece di centrarlo
                                $x = 0;
                                $y = 0;

                                // Use the template at the original position
                                $mpdf->UseTemplate($tplId, $x, $y);

                                // Add a new page only if it's not the last
                                if ($i < $pagecount) {
                                    $mpdf->AddPage();
                                }
                            }

                            // We've already written all HTML and added the original PDF
                            $html = ''; // Reset HTML to avoid writing it again
                        } else {
                            dv_debug_log("Uploaded PDF is not readable");
                            $html .= '<div style="text-align:center; color:red;">';
                            $html .= __('Unable to read the uploaded document.', 'document-viewer-plugin');
                            $html .= '</div>';
                        }
                    } catch (Exception $e) {
                        dv_debug_log("Error importing PDF: " . $e->getMessage());
                        $html .= '<div style="text-align:center; color:red;">';
                        $html .= __('Error importing document: ', 'document-viewer-plugin') . esc_html($e->getMessage());
                        $html .= '</div>';
                    }
                }
                // Gestione documenti Word (DOC/DOCX)
                else if ($uploaded_pdf['type'] === 'application/msword' || $uploaded_pdf['type'] === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    try {
                        // Scrivi prima il contenuto HTML esistente
                        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                        $html_written_during_pdf_import = true;

                        // Verifica che PHPWord sia disponibile
                        if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                            throw new Exception('PHPWord library not available');
                        }

                        // Conversione del documento Word in PDF
                        dv_debug_log("Converting Word document to PDF for export");

                        // Prepara la directory temporanea
                        $upload_dir = wp_upload_dir();
                        $temp_dir = $upload_dir['basedir'] . '/document-viewer/temp';
                        if (!file_exists($temp_dir)) {
                            wp_mkdir_p($temp_dir);
                        }

                        // Crea un nome univoco per il PDF convertito
                        $temp_pdf_file = $temp_dir . '/' . uniqid('word_') . '.pdf';

                        // Carica il documento Word
                        $phpWord = \PhpOffice\PhpWord\IOFactory::load($uploaded_pdf['tmp_name']);

                        // Configura il rendering PDF
                        \PhpOffice\PhpWord\Settings::setPdfRendererName(\PhpOffice\PhpWord\Settings::PDF_RENDERER_MPDF);
                        \PhpOffice\PhpWord\Settings::setPdfRendererPath(plugin_dir_path(__FILE__) . 'vendor/mpdf/mpdf');

                        // Salva come PDF
                        $pdfWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'PDF');
                        $pdfWriter->save($temp_pdf_file);

                        dv_debug_log("Word document converted to PDF: $temp_pdf_file");

                        // Aggiungi una nuova pagina per il documento convertito
                        $mpdf->AddPage();
                          // Aggiungi titolo centrato per la nuova pagina
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Add extra space between title and document content
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Importa tutte le pagine del PDF convertito
                        if (file_exists($temp_pdf_file) && is_readable($temp_pdf_file)) {
                            $pagecount = $mpdf->SetSourceFile($temp_pdf_file);
                            dv_debug_log("Converted PDF added with $pagecount pages");

                            // Importa tutte le pagine del PDF convertito
                            for ($i = 1; $i <= $pagecount; $i++) {
                                $tplId = $mpdf->ImportPage($i);

                                // Get the imported page's size
                                $pageSize = $mpdf->getTemplateSize($tplId);
                                $pageWidth = $pageSize['width'];
                                $pageHeight = $pageSize['height'];

                                // Posiziona il documento in alto a sinistra invece di centrarlo
                                $x = 0;
                                $y = 0;

                                // Use the template at the original position
                                $mpdf->UseTemplate($tplId, $x, $y);

                                // Aggiungi una nuova pagina solo se non è l'ultima
                                if ($i < $pagecount) {
                                    $mpdf->AddPage();
                                }
                            }

                            // Elimina il file temporaneo
                            @unlink($temp_pdf_file);
                        } else {
                            throw new Exception('Error accessing converted PDF');
                        }

                        // Abbiamo già scritto tutto l'HTML e aggiunto il PDF convertito
                        $html = ''; // Azzera HTML per evitare di scriverlo nuovamente
                    } catch (Exception $e) {
                        dv_debug_log("Error processing Word document: " . $e->getMessage());

                        // Se la conversione fallisce, procedi con un riquadro con messaggio
                        $mpdf->AddPage();
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="text-align:center; padding:20px; border:1px solid #ddd; background-color:#f8f8f8;">' .
                                        __('Il documento Word originale non può essere visualizzato: ', 'document-viewer-plugin') .
                                        esc_html($e->getMessage()) . '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Reset HTML content
                        $html = '';
                    }
                }
                else if (strpos($uploaded_pdf['type'], 'image/') === 0) {
                    // Per le immagini, aggiungere una nuova pagina e mostrare l'immagine centrata
                    try {
                        // Scrivi prima il contenuto HTML esistente
                        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                        $html_written_during_pdf_import = true;

                        // Aggiungi una nuova pagina per l'immagine
                        $mpdf->AddPage();
                          // Titolo della pagina
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Add extra space between title and document content
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        // Ottieni dimensioni originali dell'immagine
                        $img_info = getimagesize($uploaded_pdf['tmp_name']);
                        $img_width = $img_info[0];
                        $img_height = $img_info[1];

                        // Calcola dimensioni massime per il PDF (l'80% della pagina)
                        $max_width = $mpdf->w * 0.8;
                        $max_height = $mpdf->h * 0.6; // Lascia spazio per il titolo

                        // Calcola il rapporto di scala mantenendo le proporzioni
                        $scale = min($max_width / $img_width, $max_height / $img_height);
                        $new_width = $img_width * $scale;
                        $new_height = $img_height * $scale;

                        // Embed dell'immagine con styling per il centraggio e dimensionamento corretto
                        $image_data = file_get_contents($uploaded_pdf['tmp_name']);
                        $image_mime = mime_content_type($uploaded_pdf['tmp_name']);
                        $base64 = base64_encode($image_data);

                        // Tenta di convertire l'immagine in un formato vettoriale (SVG) se possibile
                        // Questo può migliorare la qualità nell'output PDF
                        $image_html = '<div style="text-align:center; width:100%; padding:20px; display:flex; justify-content:center; align-items:center;">' .
                                    '<img src="data:' . $image_mime . ';base64,' . $base64 . '" ' .
                                    'width="' . $new_width . '" height="' . $new_height . '" ' .
                                    'style="max-width:' . $new_width . 'px; margin:0 auto; display:block; object-fit:contain;" />' .
                                    '</div>';

                        $mpdf->WriteHTML($image_html, \Mpdf\HTMLParserMode::HTML_BODY);

                        // Reset HTML content since we've already written it
                        $html = '';
                    } catch (Exception $e) {
                        dv_debug_log("Error embedding image: " . $e->getMessage());
                          // Se l'incorporamento fallisce, aggiungi un messaggio di errore
                        $mpdf->AddPage();
                        $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);
                        $mpdf->WriteHTML('<div style="text-align:center; color:red; padding:20px;">' .
                                       __('Non è stato possibile incorporare l\'immagine: ', 'document-viewer-plugin') .
                                       esc_html($e->getMessage()) . '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                        $html = '';
                    }
                } else {
                    // Se non è un PDF o un'immagine, scrivi prima il contenuto HTML
                    $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
                    $html_written_during_pdf_import = true;

                    // Aggiungi una nuova pagina per il messaggio informativo
                    $mpdf->AddPage();
                      // Titolo della pagina
                    $mpdf->WriteHTML('<h2 style="text-align:center; margin-bottom:20px;">' . __('Documento caricato', 'document-viewer-plugin') . '</h2>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Add extra space between title and document content
                    $mpdf->WriteHTML('<div style="height:60px;"></div>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Messaggio informativo
                    $mpdf->WriteHTML('<div style="text-align:center; padding:50px;">' .
                                    __('Il documento originale (tipo: ' . esc_html($uploaded_pdf['type']) . ') non può essere visualizzato direttamente.', 'document-viewer-plugin') .
                                    '</div>', \Mpdf\HTMLParserMode::HTML_BODY);

                    // Reset HTML content
                    $html = '';
                }
            }

            // Add footer with page numbers
            $mpdf->SetHTMLFooter('<div style="text-align: center; font-size: 10pt; color: #7f8c8d;">' . __('Page {PAGENO} of {nb}', 'document-viewer-plugin') . '</div>');

            // Set metadata
            $mpdf->SetTitle($title);
            $mpdf->SetAuthor(get_bloginfo('name'));
            $mpdf->SetCreator('Document Viewer Plugin');

            // Write HTML to PDF if we haven't already written it when importing PDF
            if (!$html_written_during_pdf_import && !empty($html)) {
                $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
            }

            // Check if it's a preview request
            if ($is_preview) {
                // Return base64 encoded PDF for preview
                $pdfData = $mpdf->Output('', 'S');
                wp_send_json_success([
                    'pdf_data' => base64_encode($pdfData)
                ]);
                return;
            }

            // Output PDF for download
            $filename = sanitize_file_name($title . ' - Analysis.pdf');

            if (headers_sent()) {
                wp_die(__('Headers already sent, cannot output PDF', 'document-viewer-plugin'));
            }

            // Clear all existing output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Send appropriate headers
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // Output the PDF directly
            echo $mpdf->Output($filename, 'S');
            exit;
        } catch (Exception $e) {
            dv_debug_log('PDF export error: ' . $e->get_message());
            wp_send_json_error(['message' => 'Error exporting PDF: ' . $e->getMessage()]);
        }
    }

    // Check if PDF Parser is available
    private function check_pdf_parser() {
        return document_management()->check_pdf_parser();
    }

    // Extract content from PDF
    private function extract_pdf_content($file_path) {
        return document_management()->extract_pdf_content($file_path);
    }

    // Clean up extracted text for better analysis
    private function clean_extracted_text($text) {
        return document_management()->clean_extracted_text($text);
    }

    // Check if PHPWord is available
    private function check_phpword() {
        return document_management()->check_phpword();
    }

    // Extract content from DOCX
    private function extract_docx_content($file_path) {
        return document_management()->extract_docx_content($file_path);
    }

    // Extract text from PHPWord element
    private function extract_element_text($element) {
        return document_management()->extract_element_text($element);
    }

    // Get upload error message
    private function get_upload_error_message($error_code) {
        return document_management()->get_upload_error_message($error_code);
    }

    /**
     * Convert Word document to PDF for visualization
     */
    public function convert_word_to_pdf() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Verifica permessi: utenti WordPress con upload_files o utenti esterni autenticati
        if (!current_user_can('upload_files') && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Permission denied.', 'document-viewer-plugin')]);
            return;
        }

        if (empty($_FILES['word_file']) || $_FILES['word_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(['message' => __('No Word file uploaded or upload error.', 'document-viewer-plugin')]);
            return;
        }

        $file = $_FILES['word_file'];
        $result = document_management()->convert_word_to_pdf($file);

        if ($result['success']) {
            wp_send_json_success([
                'pdf_url' => $result['pdf_url'],
                'pdf_content' => $result['pdf_content']
            ]);
        } else {
           wp_send_json_error(['message' => $result['message']]);
        }
    }

    public function get_shared_log() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica che l'utente sia loggato
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => __('Devi effettuare il login per visualizzare il log.', 'document-viewer-plugin')]);
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Permission denied.', 'document-viewer-plugin')]);
            return;
        }

        $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'all';

        $log_content = dv_get_shared_log($context);
        wp_send_json_success(['content' => $log_content]);
    }

    /**
     * Gestisce il salvataggio dell'analisi del documento nel database
     */

    public function save_document_analysis() {
        // Verifica il nonce per la sicurezza
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Se l'utente non è loggato in WordPress e non è un subscriber, blocca l'accesso
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Devi effettuare il login per salvare l\'analisi.', 'document-viewer-plugin')]);
            return;
        }

        dv_debug_log('Avvio salvataggio analisi documento');

        // Recupera i dati inviati
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $query = isset($_POST['query']) ? sanitize_textarea_field($_POST['query']) : '';
        $analysis_results = isset($_POST['analysis_results']) ? $_POST['analysis_results'] : '';

        // Verifica la presenza dei dati necessari
        if (empty($title) || empty($analysis_results)) {
            dv_debug_log('Dati mancanti per il salvataggio. Titolo: ' . ($title ? 'presente' : 'mancante') . ', Analisi: ' . ($analysis_results ? 'presente' : 'mancante'));
            wp_send_json_error(['message' => __('Titolo e risultati dell\'analisi sono obbligatori.', 'document-viewer-plugin')]);
            return;
        }

        // Crea directory per i file se non esiste
        $upload_dir = wp_upload_dir();
        $document_dir = $upload_dir['basedir'] . '/document-viewer/saved';
        if (!file_exists($document_dir)) {
            wp_mkdir_p($document_dir);

            // Proteggi la directory
            $htaccess_file = $document_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                file_put_contents($htaccess_file, "# Protect Directory\nOptions -Indexes\n");
            }
        }

        // Percorsi dei file da salvare
        $logo_path = '';
        $document_path = '';

        // Gestisci upload del logo
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $logo_file = $_FILES['logo'];
            $logo_name = sanitize_file_name($logo_file['name']);
            $logo_path_temp = $document_dir . '/' . uniqid('logo_') . '_' . $logo_name;

            // Controlla che sia un'immagine
            $logo_type = wp_check_filetype($logo_name);
            if (strpos($logo_type['type'], 'image/') === 0) {
                if (move_uploaded_file($logo_file['tmp_name'], $logo_path_temp)) {
                    $logo_path = $logo_path_temp;
                    dv_debug_log('Logo salvato con successo: ' . $logo_path);
                }
            } else {
                dv_debug_log('Il file del logo non è un\'immagine valida');
            }
        }

        // Gestisci upload del documento
        if (isset($_FILES['document']) && $_FILES['document']['error'] === UPLOAD_ERR_OK) {
            $document_file = $_FILES['document'];
            $document_name = sanitize_file_name($document_file['name']);
            $document_path_temp = $document_dir . '/' . uniqid('doc_') . '_' . $document_name;

            // Verifica il tipo di file
            $allowed_types = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png', 'image/gif'];

            if (in_array($document_file['type'], $allowed_types) || strpos($document_file['type'], 'image/') === 0) {
                if (move_uploaded_file($document_file['tmp_name'], $document_path_temp)) {
                    $document_path = $document_path_temp;
                    dv_debug_log('Documento salvato con successo: ' . $document_path);
                }
            } else {
                dv_debug_log('Tipo di documento non supportato: ' . $document_file['type']);
            }
        }

        // Salva i dati nel database
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';

        // Verifica che la tabella esista
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $this->create_document_analysis_table();
        }

        // Prepara i dati per l'inserimento
        $user_id = get_current_user_id();

        // Se l'utente è un sottoscrittore esterno (non WordPress)
        if ($user_id === 0 && $is_subscriber_logged_in) {
            // Ottieni i dati del sottoscrittore
            $subscriber_data = [];
            try {
                $subscriber_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
            } catch (Exception $e) {
                dv_debug_log('Errore nella decodifica dei dati del sottoscrittore: ' . $e->getMessage());
            }

            // Usa l'ID del sottoscrittore o un valore speciale
            $external_user_id = isset($subscriber_data['id']) ? intval($subscriber_data['id']) : 999999;

            // Assicurati che l'ID sia sempre positivo e non zero
            $user_id = max(1, $external_user_id);

            dv_debug_log('Utente sottoscrittore esterno rilevato, ID assegnato: ' . $user_id);
        }

        $data = [
            'user_id' => $user_id,
            'title' => $title,
            'query' => $query,
            'analysis_results' => $analysis_results,
            'created_at' => current_time('mysql')
        ];

        // Aggiungi percorso del logo se presente
        if (!empty($logo_path)) {
            $data['logo_path'] = $logo_path;
        }

        // Aggiungi percorso del documento se presente
        if (!empty($document_path)) {
            $data['document_path'] = $document_path;
        }

        // Debug dei dati prima dell'inserimento
        dv_debug_log('Preparazione inserimento dati nel DB: ' . print_r($data, true));

        // Inserisci i dati nel database
        $result = $wpdb->insert(
            $table_name,
            $data,
            ['%d', '%s', '%s', '%s', '%s', '%s', '%s']
        );

        if ($result === false) {
            dv_debug_log('Errore durante l\'inserimento nel database: ' . $wpdb->last_error);
            wp_send_json_error(['message' => __('Errore nel salvataggio dell\'analisi. Dettagli: ', 'document-viewer-plugin') . $wpdb->last_error]);
            return;
        }

        $analysis_id = $wpdb->insert_id;
        dv_debug_log('Analisi salvata con successo. ID: ' . $analysis_id);

        // Calcolo dei token utilizzati - utilizziamo il contenuto dell'analisi salvata
        $token_count = 0;
        if (!empty($analysis_results)) {
            // Stima il numero di token (circa 4 caratteri per token in italiano)
            $token_count = strlen(strip_tags($analysis_results)) / 4;
            dv_debug_log('Stima token utilizzati per l\'analisi: ' . $token_count);
        }

        // Attiva l'azione save_document_analysis per aggiornare le statistiche
        do_action('save_document_analysis', $analysis_id, $data, $token_count);

        wp_send_json_success([
            'message' => __('Analisi salvata con successo.', 'document-viewer-plugin'),
            'analysis_id' => $analysis_id
        ]);
    }

    public function create_document_analysis_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_analysis';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            title VARCHAR(255) NOT NULL,
            logo_path VARCHAR(255),
            query TEXT NOT NULL,
            analysis_results LONGTEXT NOT NULL,
            document_path VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    /**
     * Initialize database tables using the database-setup.php functions
     *
     * This is called on plugin activation
     */
    public function initialize_database_tables() {
        // Include the database setup file
        require_once plugin_dir_path(__FILE__) . 'includes/database-setup.php';

        // Call the function to initialize tables
        wpcd_initialize_database_tables();

        // Log the operation
        dv_debug_log('Database tables initialized for non-WP users statistics');
    }

    // Funzione per recuperare le richieste predefinite dal database
    public function get_preset_queries() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';

        // Verifica se la tabella esiste
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            // Se la tabella non esiste, crea alcune richieste predefinite di esempio
            return [
                ['id' => 1, 'title' => 'Analisi finanziaria completa', 'query_text' => 'Analizza questo documento finanziario e fornisci una panoramica completa includendo punti di forza, debolezze, opportunità e rischi. Evidenzia i principali indicatori finanziari.'],
                ['id' => 2, 'title' => 'Sintesi per investitori', 'query_text' => 'Crea una sintesi di questo documento ottimizzata per potenziali investitori, evidenziando opportunità di investimento, rendimenti previsti e livelli di rischio.'],
                ['id' => 3, 'title' => 'Valutazione rischi', 'query_text' => 'Identifica e analizza tutti i potenziali rischi menzionati in questo documento, classificandoli per probabilità e impatto potenziale.'],
                ['id' => 4, 'title' => 'Conformità normativa', 'query_text' => 'Valuta la conformità di questo documento alle normative finanziarie attuali, evidenziando eventuali aree problematiche o che richiedono ulteriore attenzione.']
            ];
        }

        // Recupera le richieste predefinite dal database
        $queries = $wpdb->get_results("SELECT * FROM $table_name ORDER BY title ASC", ARRAY_A);

        // Se non ci sono richieste nel database, restituisci array vuoto
        if (empty($queries)) {
            return [];
        }

        return $queries;
    }

    // Funzione per creare la tabella delle richieste predefinite
    public function create_preset_queries_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            query_text TEXT NOT NULL,
            created_by BIGINT(20) UNSIGNED NOT NULL DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);

        // Inserisci richieste predefinite solo se la tabella è vuota
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

        if ($count == 0) {
            $preset_queries = [
                [
                    'title' => 'Analisi finanziaria completa',
                    'query_text' => 'Analizza questo documento finanziario e fornisci una panoramica completa includendo punti di forza, debolezze, opportunità e rischi. Evidenzia i principali indicatori finanziari.'
                ],
                [
                    'title' => 'Sintesi per investitori',
                    'query_text' => 'Crea una sintesi di questo documento ottimizzata per potenziali investitori, evidenziando opportunità di investimento, rendimenti previsti e livelli di rischio.'
                ],
                [
                    'title' => 'Valutazione rischi',
                    'query_text' => 'Identifica e analizza tutti i potenziali rischi menzionati in questo documento, classificandoli per probabilità e impatto potenziale.'
                ],
                [
                    'title' => 'Conformità normativa',
                    'query_text' => 'Valuta la conformità di questo documento alle normative finanziarie attuali, evidenziando eventuali aree problematiche o che richiedono ulteriore attenzione.'
                ]
            ];
            
            foreach ($preset_queries as $query) {
                $wpdb->insert(
                    $table_name,
                    [
                        'title' => $query['title'],
                        'query_text' => $query['query_text'],
                        'created_by' => get_current_user_id() ?: 1
                    ],
                    ['%s', '%s', '%d']
                );
            }
        }
    }

    /**
     * AJAX handlers specifici per Document Analizer Widget
     */

    /**
     * Clear document for Document Analizer
     */
    public function analizer_clear_document() {
        check_ajax_referer('document_viewer_nonce', 'nonce');
        
        // Verifica accesso utente (WordPress o subscriber)
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Accesso negato.', 'document-viewer-plugin')]);
            return;
        }

        dv_debug_log('Document Analizer: documento cancellato', 'analizer');
        wp_send_json_success(['message' => __('Documento cancellato con successo.', 'document-viewer-plugin')]);
    }

    /**
     * Analyze document for Document Analizer - wrapper per la funzione principale
     */
    public function analizer_analyze_document() {
        dv_debug_log('Document Analizer: richiesta di analisi ricevuta', 'analizer');
        
        // Usa la stessa logica del document viewer ma con logging specifico
        $this->analyze_document();
    }

    /**
     * Export analysis to PDF for Document Analizer
     */
    public function analizer_export_analysis_pdf() {
        dv_debug_log('Document Analizer: richiesta esportazione PDF ricevuta', 'analizer');
        
        // Usa la stessa logica del document viewer ma con logging specifico
        $this->export_analysis_pdf();
    }

    /**
     * Save analysis for Document Analizer
     */
    public function analizer_save_analysis() {
        dv_debug_log('Document Analizer: richiesta salvataggio analisi ricevuta', 'analizer');
        
        // Usa la stessa logica del document viewer ma con logging specifico
        $this->save_document_analysis();
    }

    /**
     * Advanced analysis function specific to Document Analizer
     */
    public function analizer_advanced_analysis() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        // Verifica accesso utente
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);
        if (!is_user_logged_in() && !$is_subscriber_logged_in) {
            wp_send_json_error(['message' => __('Accesso negato.', 'document-viewer-plugin')]);
            return;
        }

        $document_content = isset($_POST['document_content']) ? $_POST['document_content'] : '';
        $analysis_type = isset($_POST['analysis_type']) ? sanitize_text_field($_POST['analysis_type']) : 'standard';
        $additional_params = isset($_POST['additional_params']) ? $_POST['additional_params'] : [];

        if (empty($document_content)) {
            wp_send_json_error(['message' => __('Contenuto del documento mancante.', 'document-viewer-plugin')]);
            return;
        }

        dv_debug_log("Document Analizer: analisi avanzata richiesta - tipo: $analysis_type", 'analizer');

        try {
            // Get Document Analizer Widget instance
            if (class_exists('Document_Analizer_Widget')) {
                $analizer_widget = new Document_Analizer_Widget();
                $results = $analizer_widget->handle_advanced_analysis($document_content, $analysis_type);
                
                wp_send_json_success([
                    'results' => $results,
                    'message' => __('Analisi avanzata completata con successo.', 'document-viewer-plugin')
                ]);
            } else {
                throw new Exception('Document Analizer Widget non disponibile');
            }
        } catch (Exception $e) {
            dv_debug_log('Document Analizer: errore durante analisi avanzata - ' . $e->getMessage(), 'analizer');
            wp_send_json_error(['message' => __('Errore durante l\'analisi avanzata: ', 'document-viewer-plugin') . $e->getMessage()]);
        }
    }

    /**
     * Alternative AJAX handlers with different naming convention
     */
    public function analyze_document_analizer() {
        dv_debug_log('Document Analizer: richiesta analisi (metodo alternativo)', 'analizer');
        $this->analizer_analyze_document();
    }

    public function export_analizer_pdf() {
        dv_debug_log('Document Analizer: richiesta esportazione PDF (metodo alternativo)', 'analizer');
        $this->analizer_export_analysis_pdf();
    }

    public function save_analizer_analysis() {
        dv_debug_log('Document Analizer: richiesta salvataggio (metodo alternativo)', 'analizer');
        $this->analizer_save_analysis();
    }

    // ...existing code...
}

new Document_Viewer_Plugin();

// Inizializza il plugin
$document_viewer_plugin = new Document_Viewer_Plugin();

/**
 * Initialize Widget Help System components in the correct order
 * This ensures proper loading and prevents circular dependencies
 */
function init_widget_help_system() {
    // 1. Run migration first if needed
    if (class_exists('Widget_Help_Migration')) {
        // Check if we need to run the migration
        $migration_status = get_option('widget_help_migration_status', array());
        if (empty($migration_status['completed'])) {
            // Run automatic migration for option keys
            Widget_Help_Migration::migrate_option_keys();
        }
    }
    
    // 2. Initialize Widget Help Manager (new system)
    if (class_exists('Widget_Help_Manager')) {
        Widget_Help_Manager::get_instance();
    }
    
    // 3. Widget Help System is already initialized globally
    // No additional action needed as it's handled in its constructor
}

// Hook the initialization to WordPress init
add_action('init', 'init_widget_help_system', 15);

/**
 * Get current user information regardless of user type (WordPress or external)
 *
 * @return array User information with keys: id, type, data
 */
function fa_get_current_user_info() {
    global $document_viewer_plugin;
    return $document_viewer_plugin->get_current_user_info();
}

/**
 * Update user statistics in a transactional manner
 *
 * @param int $user_id User ID
 * @param string $user_type User type (wordpress|subscriber)
 * @param array $stats_update Statistics to update
 * @return bool Success or failure
 */
function fa_update_user_stats_transaction($user_id, $user_type, $stats_update) {
    global $document_viewer_plugin;
    return $document_viewer_plugin->update_user_stats_transaction($user_id, $user_type, $stats_update);
}

// Initialize Document Stats
$document_stats = new Document_Stats();

// Initialize Document Stats
$document_stats = new Document_Stats();

// Initialize the Help Line functionality
require_once plugin_dir_path(__FILE__) . 'includes/class-help-line.php';
help_line();

// AGGIUNGI la registrazione dell'handler AJAX dopo la definizione della classe
add_action('wp_ajax_ocr_log', 'handle_ocr_log');

/**
 * Handle OCR log messages from JavaScript
 */
function handle_ocr_log() {
    check_ajax_referer('document_viewer_nonce', 'nonce');

    $message = isset($_POST['message']) ? sanitize_text_field($_POST['message']) : '';
    $context = isset($_POST['context']) ? sanitize_text_field($_POST['context']) : 'ocr';
    $extracted_text = isset($_POST['extracted_text']) ? $_POST['extracted_text'] : '';

    // Log the message
    dv_debug_log($message, $context);

    // If we have extracted text, save it to session for potential debugging
    if (!empty($extracted_text)) {
        if (!session_id() && !headers_sent()) {
            session_start();
        }
        $_SESSION['ocr_extracted_text'] = $extracted_text;
        dv_debug_log('Testo OCR salvato in sessione (' . strlen($extracted_text) . ' caratteri)', $context);
    }

    wp_send_json_success(['message' => 'Log saved']);
}




// Includi la classe per il caricamento degli script
require_once plugin_dir_path(__FILE__) . 'includes/class-enqueue-scripts.php';

// Includi il widget Document Analizer se non già incluso
if (!class_exists('Document_Analizer_Widget')) {
    require_once plugin_dir_path(__FILE__) . 'includes/widgets/document-analizer/document-analizer-widget.php';
}

/**
 * Invalidate user statistics cache
 *
 * @param int $user_id User ID
 * @param string $user_type User type (wordpress|subscriber)
 * @return bool Success or failure
 */
function fa_invalidate_user_stats_cache($user_id, $user_type) {
    // Per ora implementiamo una versione semplice che non utilizza una cache reale
    // In futuro, se viene implementata una cache, questo metodo dovrà essere aggiornato
    dv_debug_log("Invalidazione cache statistiche per utente: $user_id (tipo: $user_type)");
    return true;
}