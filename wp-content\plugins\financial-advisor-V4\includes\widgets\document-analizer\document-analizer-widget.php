<?php
if (!defined('ABSPATH')) {
    exit;
}

// Includi la classe Document_Stats (stessa fonte dati)
require_once plugin_dir_path(dirname(__FILE__)) . '../class-document-stats.php';

/**
 * Document Analizer Widget
 * Clone robusto del Document Viewer Widget con funzioni avanzate
 */
class Document_Analizer_Widget extends WP_Widget {
    private $api_status = null;

    public function __construct() {
        parent::__construct(
            'document_analizer_widget',
            __('Document Analizer Widget', 'document-viewer-plugin'),
            array('description' => __('A robust widget to analyze and view documents with advanced features.', 'document-viewer-plugin'))
        );

        // Check API configuration on widget initialization (come Chat Model Widget)
        $this->check_api_configuration();
        
        // Register and enqueue widget specific scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles for the widget (come Chat Model Widget)
     */
    public function enqueue_scripts() {
        // Enqueue the Widget Help System JS FIRST (if not already enqueued)
        wp_register_script(
            'widget-help-system',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/widget-help-system.js',
            array('jquery'),
            DOCUMENT_ADVISOR_VERSION,
            true
        );
        wp_enqueue_script('widget-help-system');

        // Register and enqueue the Document Analizer Widget CSS with same dependencies as Document Viewer
        wp_register_style(
            'document-analizer-widget-style',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/widgets/document-analizer.css',
            array('document-viewer-style', 'text-enhancer-style', 'mega-tooltip-style'),
            DOCUMENT_ADVISOR_VERSION
        );
        wp_enqueue_style('document-analizer-widget-style');

        // Register and enqueue the Document Analizer Widget JavaScript, with same dependencies as Document Viewer
        wp_register_script(
            'document-analizer-widget-script',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/document-analizer.js',
            array('jquery', 'widget-help-system', 'document-ocr-script', 'document-stats-script', 'text-enhancer-script', 'mega-tooltip-script'),
            DOCUMENT_ADVISOR_VERSION,
            true
        );
        wp_enqueue_script('document-analizer-widget-script');

        // Localize script with AJAX parameters
        wp_localize_script(
            'document-analizer-widget-script',
            'documentAnalizerParams',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce'),
                'pluginUrl' => plugin_dir_url(dirname(dirname(__FILE__))),
                'i18n' => array(
                    'uploadError' => __('Errore durante il caricamento del file. Riprova.', 'document-viewer-plugin'),
                    'analysisError' => __('Errore durante l\'analisi del documento. Riprova.', 'document-viewer-plugin'),
                    'exportError' => __('Errore durante l\'esportazione PDF. Riprova.', 'document-viewer-plugin'),
                    'clearConfirm' => __('Sei sicuro di voler cancellare il documento corrente? Tutta l\'analisi andrà persa.', 'document-viewer-plugin'),
                    'saving' => __('Salvataggio...', 'document-viewer-plugin'),
                    'saved' => __('Salvato', 'document-viewer-plugin'),
                    'saveError' => __('Errore nel salvataggio delle modifiche', 'document-viewer-plugin'),
                    'ocrProcessing' => __('Elaborazione OCR in corso...', 'document-viewer-plugin'),
                    'ocrComplete' => __('OCR completato con successo', 'document-viewer-plugin'),
                    'ocrError' => __('Errore durante l\'elaborazione OCR', 'document-viewer-plugin'),
                    'statsError' => __('Errore nel caricamento delle statistiche', 'document-viewer-plugin'),
                    'refreshStats' => __('Aggiorna Statistiche', 'document-viewer-plugin'),
                    'statsLoading' => __('Caricamento statistiche...', 'document-viewer-plugin'),
                    'noAnalyses' => __('Nessuna analisi disponibile', 'document-viewer-plugin'),
                )
            )
        );
    }

    /**
     * Check API configuration (come Chat Model Widget)
     */
    private function check_api_configuration() {
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            $this->api_status = 'unconfigured';
            return false;
        }
        $this->api_status = 'configured';
        return true;
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];
        echo '<!-- DEBUG: Document_Analizer_Widget rendering -->';
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Show configuration notice if API is not configured (come Chat Model Widget)
        if ($this->api_status === 'unconfigured') {
            // Mostra il messaggio di configurazione solo agli amministratori
            if (current_user_can('manage_options')) {
                echo '<div class="notice notice-warning"><p>';
                echo __('Il Document Analizer non è configurato. ', 'document-viewer-plugin');
                echo '<a href="' . admin_url('options-general.php?page=document-viewer-settings') . '">';
                echo __('Configura le impostazioni API', 'document-viewer-plugin');
                echo '</a></p></div>';
            } else {
                echo '<div class="notice notice-info"><p>';
                echo __('Il Document Analizer è temporaneamente non disponibile.', 'document-viewer-plugin');
                echo '</p></div>';
            }
        }

        // Attributi personalizzati per shortcode
        $shortcode_atts = '';
        if (!empty($instance['custom_title'])) {
            $shortcode_atts = ' title="' . esc_attr($instance['custom_title']) . '"';
        }
        // Usa uno shortcode dedicato per il nuovo widget
        echo do_shortcode('[document_analizer' . $shortcode_atts . ']');
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Document Analizer', 'document-viewer-plugin');
        $custom_title = !empty($instance['custom_title']) ? $instance['custom_title'] : __('Analisi Avanzata Documento', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Widget Title:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>"
                   type="text" value="<?php echo esc_attr($title); ?>">
            <small><?php _e('This is the title that appears above the widget in the sidebar.', 'document-viewer-plugin'); ?></small>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_title')); ?>"><?php _e('Document Analizer Title:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('custom_title')); ?>"
                   type="text" value="<?php echo esc_attr($custom_title); ?>">
            <small><?php _e('This title appears inside the document analizer component.', 'document-viewer-plugin'); ?></small>
        </p>
        
        <!-- API Status Display -->
        <p>
            <strong><?php _e('API Status:', 'document-viewer-plugin'); ?></strong>
            <?php if ($this->api_status === 'configured'): ?>
                <span style="color: green;">✓ <?php _e('Configurato', 'document-viewer-plugin'); ?></span>
            <?php else: ?>
                <span style="color: red;">✗ <?php _e('Non configurato', 'document-viewer-plugin'); ?></span>
                <br><small><a href="<?php echo admin_url('options-general.php?page=document-viewer-settings'); ?>"><?php _e('Configura API', 'document-viewer-plugin'); ?></a></small>
            <?php endif; ?>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['custom_title'] = (!empty($new_instance['custom_title'])) ? sanitize_text_field($new_instance['custom_title']) : '';
        return $instance;
    }

    /**
     * Ottieni le statistiche HTML per il Document Analizer (stesso sistema del Document Viewer)
     */
    public function get_analizer_stats_html($include_header = true) {
        $user_id = get_current_user_id();
        $is_wp_user = $user_id > 0;
        $external_user_id = null;

        // Verifica se è un utente esterno non WordPress
        if (!$is_wp_user && isset($_COOKIE['fa_subscriber_login'])) {
            try {
                $cookie_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
                if (isset($cookie_data['id'])) {
                    $external_user_id = intval($cookie_data['id']);
                }
            } catch (Exception $e) {
                // Log dell'errore se necessario
            }
        }

        // Se non è né un utente WordPress né un utente esterno, mostra messaggio di login
        if (!$is_wp_user && !$external_user_id) {
            return '<div class="stats-not-logged">
                <p>Accedi per visualizzare le tue statistiche di utilizzo.</p>
            </div>';
        }

        // Ottieni le statistiche usando la stessa classe del Document Viewer
        $document_stats = new Document_Stats();
        if ($is_wp_user) {
            $stats = $document_stats->get_user_stats($user_id);
            if (empty($stats)) {
                $document_stats->initialize_user_stats($user_id);
                $stats = $document_stats->get_user_stats($user_id);
            }
        } else {
            $stats = $document_stats->get_external_user_stats($external_user_id);
            if (empty($stats)) {
                $stats = (object)[
                    'analysis_count' => 0,
                    'tokens_used' => 0,
                    'credits_available' => 0,
                    'actual_cost' => 0,
                    'tot_cost' => 0
                ];
            }
        }

        // Formattazione numeri
        $analysis_count = $stats->analysis_count;
        $tokens_used = number_format($stats->tokens_used, 0, ',', '.');
        $credits_available = number_format((float)$stats->credits_available, 2, ',', '.');
        $actual_cost = number_format((float)$stats->actual_cost, 2, ',', '.');

        // Genera HTML delle statistiche con prefisso specifico per il Document Analizer
        $html = '<div class="document-analizer-stats-container">
            <div class="stats-grid">
                <div class="stats-row costs-row">
                    <div class="stats-item cost-item">
                        <div class="stats-label">
                            Spesa Stimata
                            <span class="stats-info-icon">i</span>
                            <div class="stats-tooltip">Stima del costo basata sul numero di token utilizzati</div>
                        </div>
                        <div class="stats-value cost-highlight" id="analizer-cost-estimate">€' . esc_html($actual_cost) . '</div>
                    </div>
                    <div class="stats-item cost-item">
                        <div class="stats-label">
                            Spesa Effettiva
                            <span class="stats-info-icon">i</span>
                            <div class="stats-tooltip">Costo effettivo dell\'analisi completata</div>
                        </div>
                        <div class="stats-value cost-highlight" id="analizer-actual-cost">€' . esc_html($actual_cost) . '</div>
                    </div>
                </div>

                <div class="stats-row credit-row">
                    <div class="stats-item credit-item">
                        <div class="stats-label">
                            Credito Disponibile
                            <span class="stats-info-icon">i</span>
                            <div class="stats-tooltip">Credito disponibile per l\'esecuzione di nuove analisi</div>
                        </div>
                        <div class="stats-value credit-highlight" id="analizer-credits-available">€' . esc_html($credits_available) . '</div>
                    </div>
                </div>
            </div>
        </div>';

        // Aggiungi JavaScript per sincronizzazione credito specifico per Document Analizer
        $html .= '
        <script>
        jQuery(document).ready(function($) {
            // Funzione per sincronizzare il credito dal database per Document Analizer
            function syncAnalizerCredit() {
                if (typeof subscriberManagementAjax !== "undefined" && subscriberManagementAjax.ajax_url) {
                    $.ajax({
                        url: subscriberManagementAjax.ajax_url,
                        type: "POST",
                        data: {
                            action: "get_current_credit",
                            nonce: subscriberManagementAjax.nonce
                        },
                        success: function(response) {
                            if (response.success && response.data.credit !== undefined) {
                                const creditElement = $("#analizer-credits-available");
                                if (creditElement.length) {
                                    const formattedCredit = "€" + parseFloat(response.data.credit).toFixed(2).replace(".", ",");
                                    creditElement.text(formattedCredit);
                                    console.log("💰 Document Analizer: Credito sincronizzato dal database:", formattedCredit);
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            console.warn("⚠️ Document Analizer: Errore sincronizzazione credito:", error);
                        }
                    });
                }
            }

            // Sincronizza il credito all\'avvio
            setTimeout(syncAnalizerCredit, 1000);

            // Sincronizza il credito ogni 30 secondi
            setInterval(syncAnalizerCredit, 30000);

            // Ascolta eventi di aggiornamento credito da altri widget
            $(document).on("credit-updated", function(event, newCredit) {
                const creditElement = $("#analizer-credits-available");
                if (creditElement.length && newCredit !== undefined) {
                    const formattedCredit = "€" + parseFloat(newCredit).toFixed(2).replace(".", ",");
                    creditElement.text(formattedCredit);
                    console.log("💰 Document Analizer: Credito aggiornato da evento:", formattedCredit);
                }
            });

            console.log("✅ Document Analizer: Sincronizzazione credito inizializzata");
        });
        </script>';

        return $html;
    }
}
