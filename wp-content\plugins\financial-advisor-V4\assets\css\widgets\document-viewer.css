/**
 * Document Viewer Widget - Isolated Styles
 * Stili completamente isolati per il Document Viewer Widget
 * <PERSON><PERSON> i selettori sono prefissati per evitare conflitti
 */

/* Container principale isolato */
.document-viewer-widget {
    all: initial !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #333 !important;
    background: #fff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08) !important;
    margin: 20px 0 !important;
    padding: 30px !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 25px !important;
    position: relative !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* Reset per tutti gli elementi figli */
.document-viewer-widget *,
.document-viewer-widget *::before,
.document-viewer-widget *::after {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    text-decoration: none !important;
    list-style: none !important;
    font-family: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    background: transparent !important;
}

/* Titoli del widget */
.document-viewer-widget h3 {
    margin-bottom: 20px !important;
    font-size: 1.3rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    letter-spacing: 0.02em !important;
    background: transparent !important;
    padding: 0 !important;
    border: none !important;
}

/* Colonna statistiche (sinistra) */
.document-viewer-widget .stats-column {
    flex: 0 0 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 8px !important;
    height: fit-content !important;
    position: relative !important;
}

.document-viewer-widget .stats-column h3 {
    color: white !important;
    margin-bottom: 15px !important;
    font-size: 1.1rem !important;
    text-align: center !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid rgba(255,255,255,0.3) !important;
}

/* Sezioni statistiche */
.document-viewer-widget .stats-section {
    margin-bottom: 20px !important;
    background: rgba(255,255,255,0.1) !important;
    border-radius: 6px !important;
    overflow: hidden !important;
}

.document-viewer-widget .stats-section-header {
    padding: 12px 15px !important;
    background: rgba(255,255,255,0.2) !important;
    cursor: pointer !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
}

.document-viewer-widget .stats-section-content {
    padding: 15px !important;
    display: block !important;
}

/* Info utente */
.document-viewer-widget .user-info {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    padding: 10px !important;
    background: rgba(255,255,255,0.1) !important;
    border-radius: 6px !important;
}

.document-viewer-widget .user-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background: rgba(255,255,255,0.3) !important;
    margin-right: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
}

.document-viewer-widget .user-details {
    flex: 1 !important;
}

.document-viewer-widget .user-name {
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    margin-bottom: 2px !important;
}

.document-viewer-widget .user-role {
    font-size: 0.8rem !important;
    opacity: 0.8 !important;
}

/* Griglia statistiche */
.document-viewer-widget .stats-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.document-viewer-widget .stats-row {
    display: flex !important;
    gap: 10px !important;
}

.document-viewer-widget .stats-item {
    flex: 1 !important;
    background: rgba(255,255,255,0.1) !important;
    padding: 10px !important;
    border-radius: 4px !important;
    text-align: center !important;
}

.document-viewer-widget .stats-label {
    font-size: 0.8rem !important;
    opacity: 0.9 !important;
    margin-bottom: 5px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 5px !important;
}

.document-viewer-widget .stats-value {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: white !important;
}

.document-viewer-widget .cost-highlight {
    color: #ffd700 !important;
}

.document-viewer-widget .credit-highlight {
    color: #90ee90 !important;
}

/* Colonna form (centro) */
.document-viewer-widget .document-form-column {
    flex: 1 !important;
    min-width: 350px !important;
    background: #fff !important;
    padding: 20px !important;
    border-radius: 8px !important;
    border: 1px solid #e5e5e5 !important;
}

.document-viewer-widget .document-form-column h3 {
    color: #333 !important;
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
    border-bottom: 2px solid #f0f0f0 !important;
}

/* Form rows */
.document-viewer-widget .form-row {
    margin-bottom: 20px !important;
}

.document-viewer-widget .form-row label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 600 !important;
    color: #555 !important;
    font-size: 0.9rem !important;
}

.document-viewer-widget .form-row input[type="text"],
.document-viewer-widget .form-row input[type="file"],
.document-viewer-widget .form-row textarea,
.document-viewer-widget .form-row select {
    width: 100% !important;
    padding: 10px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    font-family: inherit !important;
    background: #fff !important;
    color: #333 !important;
    transition: border-color 0.3s ease !important;
}

.document-viewer-widget .form-row input[type="text"]:focus,
.document-viewer-widget .form-row textarea:focus,
.document-viewer-widget .form-row select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.document-viewer-widget .form-row textarea {
    resize: vertical !important;
    min-height: 80px !important;
}

/* Buttons */
.document-viewer-widget button {
    background: #667eea !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-right: 10px !important;
    font-family: inherit !important;
}

.document-viewer-widget button:hover {
    background: #5a67d8 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.document-viewer-widget button:active {
    transform: translateY(0) !important;
}

.document-viewer-widget button:disabled {
    background: #ccc !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* File upload styling */
.document-viewer-widget .file-upload-container {
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
}

.document-viewer-widget .file-upload-input {
    flex: 0 0 auto !important;
}

.document-viewer-widget input[type="file"] {
    display: none !important;
}

.document-viewer-widget label[for*="upload"] {
    background: #f8f9fa !important;
    border: 2px dashed #ddd !important;
    padding: 15px 20px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-block !important;
    font-weight: 500 !important;
    color: #666 !important;
    text-align: center !important;
    min-width: 150px !important;
}

.document-viewer-widget label[for*="upload"]:hover {
    background: #e9ecef !important;
    border-color: #667eea !important;
    color: #667eea !important;
}

/* Document info inline */
.document-viewer-widget .document-info-inline {
    flex: 1 !important;
    font-size: 0.9em !important;
    background-color: #f8f9fa !important;
    padding: 10px 15px !important;
    border-radius: 4px !important;
    border-left: 3px solid #667eea !important;
    margin-left: 10px !important;
}

.document-viewer-widget .document-info-inline span {
    margin-right: 12px !important;
    white-space: nowrap !important;
    font-size: 0.85rem !important;
    color: #666 !important;
}

/* Preset queries */
.document-viewer-widget .preset-queries-container {
    margin-bottom: 10px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.document-viewer-widget .preset-queries-select {
    flex: 1 !important;
}

.document-viewer-widget .preset-info-tip {
    width: 20px !important;
    height: 20px !important;
    border-radius: 50% !important;
    background: #667eea !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    cursor: help !important;
}

/* Export actions */
.document-viewer-widget .export-actions {
    margin-top: 20px !important;
    padding-top: 20px !important;
    border-top: 1px solid #eee !important;
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

.document-viewer-widget .save-result-message {
    margin-left: 15px !important;
    padding: 5px 10px !important;
    border-radius: 4px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
}

.document-viewer-widget .save-result-message.success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.document-viewer-widget .save-result-message.error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

/* Colonna display (destra) */
.document-viewer-widget .document-display-column {
    flex: 1 !important;
    min-width: 350px !important;
    background: #fff !important;
    padding: 20px !important;
    border-radius: 8px !important;
    border: 1px solid #e5e5e5 !important;
}

.document-viewer-widget .document-display-column h3 {
    color: #333 !important;
    margin-bottom: 15px !important;
    padding-bottom: 8px !important;
    border-bottom: 2px solid #f0f0f0 !important;
}

/* Analysis results */
.document-viewer-widget #analysis-results {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 6px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    min-height: 200px !important;
    position: relative !important;
}

.document-viewer-widget #analysis-results h5 {
    color: #2d3748 !important;
    font-size: 1.5em !important;
    line-height: 1.4 !important;
    margin: 0 0 15px 0 !important;
    font-weight: 600 !important;
}

/* Document display */
.document-viewer-widget #document-display {
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    background: #fff !important;
    position: relative !important;
}

.document-viewer-widget #document-frame {
    width: 100% !important;
    height: 500px !important;
    border: none !important;
    display: block !important;
}

/* Zoom controls */
.document-viewer-widget .zoom-controls {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    display: flex !important;
    gap: 5px !important;
    z-index: 10 !important;
}

.document-viewer-widget .zoom-btn {
    width: 35px !important;
    height: 35px !important;
    border-radius: 50% !important;
    background: rgba(102, 126, 234, 0.9) !important;
    color: white !important;
    border: none !important;
    font-size: 18px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    padding: 0 !important;
}

.document-viewer-widget .zoom-btn:hover {
    background: rgba(90, 103, 216, 0.9) !important;
    transform: scale(1.1) !important;
}

/* Notification area */
.document-viewer-widget .document-notification-area {
    background: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 4px !important;
    padding: 10px 15px !important;
    margin-bottom: 15px !important;
    color: #856404 !important;
    font-size: 0.9rem !important;
}

.document-viewer-widget .document-notification-area.success {
    background: #d4edda !important;
    border-color: #c3e6cb !important;
    color: #155724 !important;
}

.document-viewer-widget .document-notification-area.error {
    background: #f8d7da !important;
    border-color: #f5c6cb !important;
    color: #721c24 !important;
}

/* Login required */
.document-viewer-widget .document-viewer-login-required,
.document-viewer-widget .login-required {
    text-align: center !important;
    padding: 40px 20px !important;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 6px !important;
    color: #6c757d !important;
}

.document-viewer-widget .document-viewer-login-required p,
.document-viewer-widget .login-required p {
    margin-bottom: 15px !important;
    font-size: 1rem !important;
    color: #6c757d !important;
}

.document-viewer-widget .document-viewer-login-required a,
.document-viewer-widget .login-required a {
    background: #667eea !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 4px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    display: inline-block !important;
    transition: all 0.3s ease !important;
}

.document-viewer-widget .document-viewer-login-required a:hover,
.document-viewer-widget .login-required a:hover {
    background: #5a67d8 !important;
    transform: translateY(-1px) !important;
}

/* Logout button */
.document-viewer-widget .stats-logout-button {
    margin-left: auto !important;
}

.document-viewer-widget .stats-logout-link {
    background: rgba(255,255,255,0.2) !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 4px !important;
    text-decoration: none !important;
    font-size: 0.8rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
    transition: all 0.3s ease !important;
}

.document-viewer-widget .stats-logout-link:hover {
    background: rgba(255,255,255,0.3) !important;
}

.document-viewer-widget .stats-logout-link .dashicons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
}

/* Recent analyses */
.document-viewer-widget .recent-analyses-list {
    max-height: 200px !important;
    overflow-y: auto !important;
}

.document-viewer-widget .recent-analysis-item {
    padding: 8px 12px !important;
    background: rgba(255,255,255,0.1) !important;
    border-radius: 4px !important;
    margin-bottom: 8px !important;
    font-size: 0.85rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.document-viewer-widget .recent-analysis-item:hover {
    background: rgba(255,255,255,0.2) !important;
}

.document-viewer-widget .recent-analysis-title {
    font-weight: 600 !important;
    margin-bottom: 2px !important;
}

.document-viewer-widget .recent-analysis-date {
    opacity: 0.8 !important;
    font-size: 0.8rem !important;
}

/* Tooltips */
.document-viewer-widget .stats-info-icon {
    display: inline-block !important;
    width: 14px !important;
    height: 14px !important;
    border-radius: 50% !important;
    background: rgba(255,255,255,0.3) !important;
    color: white !important;
    font-size: 10px !important;
    text-align: center !important;
    line-height: 14px !important;
    cursor: help !important;
    position: relative !important;
    margin-left: 5px !important;
}

.document-viewer-widget .stats-tooltip {
    position: absolute !important;
    bottom: 120% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0,0,0,0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    font-size: 0.8rem !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    pointer-events: none !important;
}

.document-viewer-widget .stats-info-icon:hover .stats-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Toggle icons */
.document-viewer-widget .toggle-icon {
    transition: transform 0.3s ease !important;
    font-size: 12px !important;
}

.document-viewer-widget .toggle-icon.expanded::before {
    content: "▼" !important;
}

.document-viewer-widget .toggle-icon.collapsed::before {
    content: "▶" !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .document-viewer-widget {
        flex-direction: column !important;
        padding: 15px !important;
    }

    .document-viewer-widget .stats-column,
    .document-viewer-widget .document-form-column,
    .document-viewer-widget .document-display-column {
        flex: none !important;
        min-width: auto !important;
        max-width: none !important;
        width: 100% !important;
    }

    .document-viewer-widget .stats-row {
        flex-direction: column !important;
    }

    .document-viewer-widget .file-upload-container {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .document-viewer-widget .document-info-inline {
        margin-left: 0 !important;
        margin-top: 10px !important;
    }
}
